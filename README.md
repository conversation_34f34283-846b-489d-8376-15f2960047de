
<div align="center">
<h3 align="center">
<img src="https://i.imgur.com/l5v0Qp2.png" width="150" />

PO105 Mobile Application
</h3>


<h3>Developed with ❤️ and </h3>

<p align="center">

<img src="https://img.shields.io/badge/DigitalOcean-%230167ff.svg?logo=digitalOcean&logoColor=white" alt="DigitalOcean" />
<img src="https://img.shields.io/badge/Postgres-%23316192.svg?logo=postgresql&logoColor=white" alt="PostgreSQL" />
<img src="https://img.shields.io/badge/Node.js-6DA55F?logo=node.js&logoColor=white" alt="NodeJS" />
<img src="https://img.shields.io/badge/Express.js-%23404d59.svg?logo=express&logoColor=%2361DAFB" alt="Express" />

</p>

<h5><em>In partnership with Midlands Power Networks, Ltd.</em></h5>

</div>


---

## 📒 Table of Contents
- [📒 Table of Contents](#-table-of-contents)
- [📍 Overview](#-overview)
- [⚙️ Features](#%EF%B8%8F-features)
- [📂 Project Structure](#-project-structure)
- [🗺 Technical Roadmap](#-technical-roadmap)
---


## 📍 Overview

**AI-Powered Emergency Response Platform**

 > This project is a software-driven emergency response platform aimed at improving service continuity and efficiency in the High Voltage (HV) sector. The focus is on optimising emergency response procedures by utilising AI to make the process of locating and dispatching a Senior Authorised Person (SAP) as efficient and streamlined as possible.

 > The concept broadly mimicks an Uber-like platform that allows HV customers experiencing an outage to easily request a SAP, ensuring prompt response and reducing financial losses. Additionally, the software is planned to incorporate equipment monitoring features, alerting customers to potential issues with transformers and switchgear to prevent outages before they occur. A built-in customer support system will assist with initial fault triaging, providing users with quick guidance and resources.

 > This project is a collaborative effort between Staffordshire University and Midlands Power Networks Ltd. through a Knowledge Transfer Partnership (KTP), and is informed by industry-specific requirements to elevate emergency response standards in the HV sector across the UK.

---

## ⚙️ Features

◦ **24/7 Emergency Response**: Efficient geolocation-based SAP dispatching, ensuring prompt attendance when emergencies arise.

◦ **AI-Powered Preliminary Triage**: Using state-of-the-art LLMs and RAG models, a virtual assistant will guide customers to better assess the nature of an emergency and give insights to the attending SAP.

◦ **HV/LV Suppliers Directory**: Partners and SAPs can access an up-to-date directory of HV/LV Suppliers, as well as their stock.

◦ **Equipment Replacement Recommendation System (possible patent)**: A recommendation system built to provide potentially cheaper combinations of equipment/replacement parts based on the aggragated suppliers' current stock.

◦ **Substation Health Monitoring using AI**: Using thermographic input in conjunction with other sensors' readings, an AI model can accurately determine the likelihood of equipment failure.

◦ **Proactive Maintenance & Analytics System**: Customers will be provided with a detailed, real-time analysis of their HV/LV equipment. Additionally, a notification system will be set to autonomously advise customers to request inspections to mitigate potential failures.


---


## 📂 Project Structure

:bangbang: ***The source and the project structure below should be kept private for security purposes.***

```cpp
.PO105 App Monorepo
├─ .gitignore
├─ README.md
├─ package.json                                           // Project description
└─ packages                                               // Monolithic repository
   ├─ backend                                             // Server-side code
   ├─ frontend                                            // Client-side code
   └─ shared                                              // Shared components

```

---


## 🗺 Technical Roadmap

*Note: the following list is just a notepad for future technical improvements, not a roadmap for the platform's features.* 

#### QoS / UX
  - [ ] ℹ️ Task Q1: *System Name:*

#### Security
  - [ ] ℹ️ Task S1: 

#### Functionality
  - [ ] ℹ️ Task F1: 

#### Management
  - [ ] ℹ️ Task M1: 

#### Reliability
  - [ ] ℹ️ Task R1:

#### Privacy / Legality
  - [ ] ℹ️ Task L1: 


