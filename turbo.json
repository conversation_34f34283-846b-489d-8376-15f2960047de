{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"lint": {"dependsOn": ["^lint"]}, "build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "test": {"dependsOn": ["^test"]}, "dev": {"cache": false, "persistent": true}, "generate-types": {"dependsOn": ["^generate-types"]}, "clean": {"dependsOn": ["^clean"]}, "start": {"dependsOn": ["^start"]}}}