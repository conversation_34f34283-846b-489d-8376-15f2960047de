# AI-Powered Emergency Response Platform (PO105)

The project, PowerOff105 , aims to develop an innovative AI-powered emergency response platform speciﬁcally designed for private High Voltage electrical infrastructure within the UK.

Currently, a signiﬁcant gap exists in the market, as national hotlines primarily address public network faults, leaving private industrial, commercial, and institutional sites (e.g., hospitals, data centres) without a dedicated, rapid-response mechanism during critical High Voltage equipment failures. PowerOff105 addresses this by proposing a multi-sided marketplace platform, analogous to ride-hailing services, connecting Customers experiencing emergencies with qualiﬁed engineers based on proximity, qualiﬁcations, and urgency.

As per the work-plan, Stage 1 involved a comprehensive literature review conﬁrming the market gap and exploring relevant technologies, including AI for predictive maintenance and fault triage, suitable technical stacks (React Native, NodeJS, PostgreSQL/PostGIS), secure authentication methods, and geospatial functionalities. An assessment of the initial work plan led to a reﬁned, Agile-based sprint structure focusing on iterative development:

• Sprint 1: Base Application

• Sprint 2: AI & Monitoring Features

• Sprint 3: Maintenance & Final Release The platform aims to reduce downtime and improve safety by streamlining engineers’ despatch, providing real-time incident tracking, facilitating communication, and leveraging AI for proactive fault detection and optimised response. This initial stage establishes the project scope, technical foundations, and development road-map necessary to build a robust and commercially viable solution addressing the identiﬁed critical need in the High Voltage sector.
## Key Concepts

- **HV (High Voltage)**: Indicates industrial-level electrical infrastructures (e.g., substations).  
- **Customer**: A company that owns or manages one or more industrial sites containing HV equipment.  
- **Site**: A physical location belonging to a Customer, typically containing HV assets such as **substations**.  
- **Substation**: A physical structure housing HV equipment, including transformers, switches, and other critical components. 
- **Equipment**: In the database, `po105_management.equipment` contains unique records for each HV equipment item. Those are referenced by a substation as `installed_equipment` (where 1 equipment can be referenced multiple times by substations to reduce redundancy).
- **Site Manager**: An individual (or multiple individuals) in the Customer's employment who can declare emergencies and request assistance.  
- **SAP (Senior Authorised Person)**: A qualified HV engineer who responds to emergencies and performs onsite repairs or interventions.  
- **Partner**: A company that employs or contracts SAPs (an SAP may be hired by a Partner or be self-employed).  

## Relationship Overview

1. **Customer** ↔ **Site**  
   - One Customer can own or manage multiple Sites. Each Site houses one or more HV substations.  

2. **Site Manager** ↔ **Emergency Declaration**  
   - A Site Manager can declare an emergency if a substation or other HV equipment fails or shows critical issues.  

3. **SAP**  
   - Receives emergency notifications from Site Managers in real time.  
   - Travels to the site to resolve faults, leveraging site-specific data (equipment history, safety records).  

4. **Partner**  
   - Employs multiple SAPs who can be dispatched to emergencies. Alternatively, a self-employed SAP can handle requests independently.  

## Workflow

1. **Emergency Occurs**  
   - A Site Manager at a Customer's site detects or is alerted to a fault in the HV substation.

2. **Declare Emergency**  
   - The Site Manager uses the PO105 platform to raise an emergency request.  
   - Key details (location, substation ID, type of fault) are entered.

3. **SAP Selection**  
   - The platform locates nearby **SAPs** (considering distance, availability, and special requirements).  
   - Site Manager can choose or confirm the most suitable SAP (e.g., based on proximity, ratings, or expertise).

4. **SAP Accepts Callout**  
   - The chosen SAP receives the emergency callout.  
   - SAP reviews site info (maintenance logs, prior issues) and confirms dispatch.

5. **Onsite Resolution**  
   - SAP arrives at the substation to diagnose and fix the issue.  
   - If needed, the SAP may involve **Partner** resources (equipment, spare parts) or coordinate with the Customer's staff.

6. **Completion & Reporting**  
   - Once the emergency is resolved, the SAP logs the resolution details and any follow-up actions.  
   - The platform updates the Site Manager and the Customer's records (for billing, future predictive maintenance, etc.).

## Emphasis

- **Multi-Sided Marketplace**: Similar to ride-hailing apps, but specialized for HV emergencies.  
- **Customer-Centric**: Site Managers have direct control—declaring emergencies and selecting SAPs—while the system streamlines workflows and data sharing.

## Technical Architecture

### Monorepo Structure
The project utilizes a monorepo structure managed by tools like Turborepo or Nx (assumption, verify actual tool) to organize the different parts of the application:
- `packages/backend`: Contains the Node.js/Express API server responsible for business logic, data access, and handling client requests.
- `packages/frontend`: (Presumed) Holds the user interface code (e.g., React, Vue, Angular) consumed by Site Managers and SAPs.
- `packages/shared`: A crucial package containing code shared between the frontend and backend, primarily TypeScript interfaces.

### Shared Interfaces (`packages/shared/src/interfaces`)
To ensure type safety and consistent data contracts between the frontend and backend:
- **`entities/`**: Contains database entity interfaces automatically generated by Kanel from the PostgreSQL schema (e.g., `po105_core`, `po105_management`). These represent the structure of database tables.
- **`dto/`**: Contains custom Data Transfer Object (DTO) TypeScript interfaces.
    - Core, reusable DTOs (e.g., `PartnerDto`, `SiteDto`) representing common data shapes reside directly within this folder.
    - **`dto/requests/`**: Holds interfaces defining the exact structure of API request payloads (e.g., `LoginRequest`).
    - **`dto/responses/`**: Holds interfaces defining the exact structure of API response payloads (e.g., `LoginResponse`).
    - All manually created interfaces for communication should reside within the appropriate `dto` subfolder. **Do not** define DTO types directly within the backend or frontend code.


### Backend Layering
The backend follows a standard layered architecture for separation of concerns:
1.  **Router (`packages/backend/src/routes/`)**: Defines API endpoints (e.g., `/auth/login`, `/api/sites`). Responsible for parsing incoming requests, applying necessary middleware (like authentication, validation), and directing requests to the appropriate Controller.
2.  **Controller (`packages/backend/src/controllers/`)**: Acts as an intermediary between the Router and the Service layer. Extracts relevant data from the request object (body, params, query) and calls the corresponding Service method(s). Formats the response received from the Service before sending it back.
3.  **Service (`packages/backend/src/services/`)**: Contains the core business logic. Orchestrates operations, validates data according to business rules, interacts with external services (like Supabase), and calls Query layer functions to interact with the database.
4.  **Query Layer (`packages/backend/src/database/queries/` or similar)**: Handles all direct database interactions, typically using an ORM or query builder like Kysely. Constructs and executes SQL queries (often complex joins) to fetch or modify data based on requests from the Service layer. It translates database results into formats usable by the Service layer.

### Supabase Integration
Supabase plays a key role in the platform:
- **Authentication**: Supabase Auth is used for user identity management (signup, password resets, potentially social logins). **Note:** The current backend code (`AuthService.ts`) includes logic for verifying password hashes (`bcrypt.compare`), suggesting local password storage might also be in use alongside Supabase. **This needs clarification:** Is Supabase *only* for identity/signup, with passwords managed locally afterward, or is the local password check outdated? Ensure the documentation reflects the definitive authentication strategy.
- **Storage**: Supabase Storage is used for storing files like profile pictures or site images. While the files reside in Supabase, metadata (file URL, owner, related entity links) is stored in the application's own database (likely within a `po105_storage` schema) for easier querying and relationship management within the platform's context.

## Login Flow

1.  **Login Request**: Frontend sends `POST /auth/login` with email and password to the backend (`auth_router.ts` -> `AuthController.ts`).
2.  **Authentication Service**: `AuthService.ts` receives credentials.
    *   It fetches user auth details (including `password_hash` and `user_type`) from the database via `AuthQueries`.
    *   It verifies the provided password against the stored hash using `bcrypt`. (**Needs clarification regarding Supabase Auth role**).
    *   If credentials are valid, it fetches basic user details (name) via `AuthQueries`.
    *   It generates a JWT containing `userId`, `email`, and `role` (`user_type`).
3.  **Login Response**: The backend responds with the JWT and basic user information (`id`, `email`, `name`, `role`).
4.  **Post-Login Data Fetching**:
    *   The frontend stores the JWT.
    *   Using the JWT for authorization, the frontend makes *separate* requests to protected API endpoints (e.g., `/api/sap/profile` or `/api/sites`) handled by `ProtectedRouter`.
    *   These protected endpoints trigger corresponding Controllers, Services, and Queries to fetch the detailed, role-specific data (SAP profile + Partner info, or Site Manager + Company/Sites info) required for the user's dashboard.

*This updated flow reflects that extended role-specific data is fetched *after* the initial login/authentication step.*
