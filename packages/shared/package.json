{"name": "@po105-app/shared", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "start": "tsc", "clean": "rm -rf dist", "prepare": "pnpm run build", "generate-types": "npx kanel", "lint": "echo \"No linting configured yet\" && exit 0"}, "devDependencies": {"@types/geojson": "^7946.0.16", "kanel": "^3.11.1", "kanel-kysely": "^0.6.1", "kysely": "^0.27.6", "typescript": "^5.6.2"}}