{"compilerOptions": {"target": "es2017", "module": "commonjs", "declaration": true, "composite": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": "./", "paths": {"@interfaces/*": ["src/interfaces/*"], "@types/*": ["src/types/*"], "@src/*": ["src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}