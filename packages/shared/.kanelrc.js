/* IMPORTS ================================================================= */
const { makeKyselyHook } = require("kanel-kysely");
const path = require('path');

/* CUSTOM HOOKS ============================================================ */

OUTPUT_PATH = './src/interfaces/entities';

// function toKebabCase(filePath) {
//     // Get the relative path from OUTPUT_PATH
//     const relativePath = path.relative(OUTPUT_PATH, filePath);
//     if (!relativePath) return filePath;

//     // Split the path and get the schema name (first part)
//     const parts = relativePath.split(path.sep);
//     if (parts.length < 1) return filePath;

//     const schemaName = parts[0];
//     const kebabSchemaName = schemaName.replace(/_/g, '-');
    
//     // Reconstruct the path with kebab-cased schema name
//     parts[0] = kebabSchemaName;
//     return path.join(OUTPUT_PATH, ...parts);
// }

// const schemaNameRecase = (output) => 
//     Object.fromEntries(
//         Object.entries(output).map(([path, fileContents]) => [
//             toKebabCase(path),
//             fileContents
//         ]),
//     );

/* CONFIG ================================================================== */

module.exports = {

    connection: {
        host: 'aws-0-eu-west-2.pooler.supabase.com',
        port: 5432,
        database: 'postgres',
        user: 'postgres.xlqndmuohqufwrqggmsb',
        password: 'QYUoRPqr46fyZLX9'
    },
    outputPath: OUTPUT_PATH,
    
    // Generate types with Kysely's features
    preRenderHooks: [
        makeKyselyHook({
            includeSchemaNameInTableName: true
        })
    ],

    // Schema settings - include all schemas from the database
    schemas: [
        'po105_core',
        'po105_auditing',
        'po105_billing',
        'po105_job',
        'po105_management',
        'po105_storage'
    ],
    
    // Customize generated type names
    customTypeMap: {
        'pg_catalog.timestamp': 'Date',
        'pg_catalog.timestamptz': 'Date',
        'pg_catalog.interval': 'string',
        // PostGIS type mappings
        'geometry': 'import("geojson").Geometry',
        'geography': 'import("geojson").Geometry',
        'extensions.geometry': 'import("geojson").Geometry',
        'extensions.geography': 'import("geojson").Geometry'
    },

    // Customize generated interface names
    transformers: {
        tableNames: (name) => {
            return name
                .split('_')
                .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
                .join('');
        },
        columnNames: (name) => name
    }
};