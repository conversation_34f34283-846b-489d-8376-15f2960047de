
/**
 * PostGIS Type Definitions
 * 
 * This file provides TypeScript definitions for working with PostGIS geometry and geography types
 * when using <PERSON><PERSON><PERSON> with a PostgreSQL database that has the PostGIS extension installed.
 */

/**
 * Base interface for PostGIS geometry data
 */
export interface PostGISGeometryBase {
    type: string;
    coordinates: number[] | number[][] | number[][][] | number[][][][];
    srid?: number;
  }
  
  /**
   * Represents a PostGIS geometry type
   * Used for BNG coordinates (EPSG:27700) in our schema
   */
  export interface PostGISGeometry extends PostGISGeometryBase {
    // Additional properties specific to geometry type if needed
  }
  
  /**
   * Represents a PostGIS geography type
   * Used for WGS84 coordinates (EPSG:4326) in our schema
   */
  export interface PostGISGeography extends PostGISGeometryBase {
    // Additional properties specific to geography type if needed
  }
  
  /**
   * Represents a Point geometry
   * Example: { type: 'Point', coordinates: [longitude, latitude], srid: 4326 }
   */
  export interface PointGeometry extends PostGISGeometryBase {
    type: 'Point';
    coordinates: [number, number]; // [x, y] or [longitude, latitude]
  }
  
  /**
   * Represents a Polygon geometry
   * Example: { type: 'Polygon', coordinates: [[[lon1, lat1], [lon2, lat2], ...]], srid: 4326 }
   */
  export interface PolygonGeometry extends PostGISGeometryBase {
    type: 'Polygon';
    coordinates: number[][][]; // Array of linear rings (first is exterior, rest are holes)
  }
  
  /**
   * Helper type to represent the GeoJSON format that would be used for serialization
   */
  export type GeoJSON = {
    type: string;
    coordinates: any;
    srid?: number;
    crs?: {
      type: string;
      properties: {
        name: string;
      };
    };
  };
  
  /**
   * Type guards to check geometry types
   */
  export function isPointGeometry(geom: any): geom is PointGeometry {
    return geom && geom.type === 'Point' && Array.isArray(geom.coordinates) && geom.coordinates.length === 2;
  }
  
  export function isPolygonGeometry(geom: any): geom is PolygonGeometry {
    return geom && geom.type === 'Polygon' && Array.isArray(geom.coordinates);
  } 