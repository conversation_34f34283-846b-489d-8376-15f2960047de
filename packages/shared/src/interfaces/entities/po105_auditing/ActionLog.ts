// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { SessionLogId } from './SessionLog';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_auditing.action_log */
export type ActionLogId = string & { __brand: 'ActionLogId' };

/** Represents the table po105_auditing.action_log */
export default interface ActionLogTable {
  id: ColumnType<ActionLogId, ActionLogId | undefined, ActionLogId>;

  session_id: ColumnType<SessionLogId | null, SessionLogId | null, SessionLogId | null>;

  action_type: ColumnType<string, string, string>;

  details: ColumnType<unknown | null, unknown | null, unknown | null>;

  action_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type ActionLog = Selectable<ActionLogTable>;

export type NewActionLog = Insertable<ActionLogTable>;

export type ActionLogUpdate = Updateable<ActionLogTable>;
