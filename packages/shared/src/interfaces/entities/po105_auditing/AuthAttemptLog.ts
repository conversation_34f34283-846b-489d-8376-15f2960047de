// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as AuthTypeEnum } from './AuthTypeEnum';
import type { default as AuthStatusEnum } from './AuthStatusEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_auditing.auth_attempt_log */
export type AuthAttemptLogId = string & { __brand: 'AuthAttemptLogId' };

/** Represents the table po105_auditing.auth_attempt_log */
export default interface AuthAttemptLogTable {
  id: ColumnType<AuthAttemptLogId, AuthAttemptLogId | undefined, AuthAttemptLogId>;

  auth_type: ColumnType<AuthTypeEnum, AuthTypeEnum, AuthTypeEnum>;

  auth_status: ColumnType<AuthStatusEnum, AuthStatusEnum, AuthStatusEnum>;

  auth_status_other: ColumnType<string | null, string | null, string | null>;

  device_uid: ColumnType<string | null, string | null, string | null>;

  ip_address: ColumnType<string | null, string | null, string | null>;

  additional_data: ColumnType<unknown | null, unknown | null, unknown | null>;

  attempt_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type AuthAttemptLog = Selectable<AuthAttemptLogTable>;

export type NewAuthAttemptLog = Insertable<AuthAttemptLogTable>;

export type AuthAttemptLogUpdate = Updateable<AuthAttemptLogTable>;
