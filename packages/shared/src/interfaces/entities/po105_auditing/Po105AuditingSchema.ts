// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as SessionLogTable } from './SessionLog';
import type { default as RegistrationLogTable } from './RegistrationLog';
import type { default as ActionLogTable } from './ActionLog';
import type { default as AuthAttemptLogTable } from './AuthAttemptLog';
import type { default as ErrorLogTable } from './ErrorLog';

export default interface Po105AuditingSchema {
  'po105_auditing.session_log': SessionLogTable;

  'po105_auditing.registration_log': RegistrationLogTable;

  'po105_auditing.action_log': ActionLogTable;

  'po105_auditing.auth_attempt_log': AuthAttemptLogTable;

  'po105_auditing.error_log': ErrorLogTable;
}
