// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from '../po105_core/User';
import type { AuthAttemptLogId } from './AuthAttemptLog';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_auditing.session_log */
export type SessionLogId = string & { __brand: 'SessionLogId' };

/** Represents the table po105_auditing.session_log */
export default interface SessionLogTable {
  id: ColumnType<SessionLogId, SessionLogId | undefined, SessionLogId>;

  user_id: ColumnType<UserId, UserId, UserId>;

  attempt_id: ColumnType<AuthAttemptLogId | null, AuthAttemptLogId | null, AuthAttemptLogId | null>;

  session_duration: ColumnType<string | null, string | null, string | null>;

  session_refreshes: ColumnType<boolean | null, boolean | null, boolean | null>;

  device_uid: ColumnType<string | null, string | null, string | null>;

  device_type: ColumnType<string | null, string | null, string | null>;

  device_model: ColumnType<string | null, string | null, string | null>;

  device_model_id: ColumnType<string | null, string | null, string | null>;

  os_version: ColumnType<string | null, string | null, string | null>;

  ip_address: ColumnType<string | null, string | null, string | null>;

  app_version: ColumnType<string | null, string | null, string | null>;

  additional_data: ColumnType<unknown | null, unknown | null, unknown | null>;

  session_start_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  session_end_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type SessionLog = Selectable<SessionLogTable>;

export type NewSessionLog = Insertable<SessionLogTable>;

export type SessionLogUpdate = Updateable<SessionLogTable>;
