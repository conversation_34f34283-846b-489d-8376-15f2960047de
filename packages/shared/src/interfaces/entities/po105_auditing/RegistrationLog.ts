// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from '../po105_core/User';
import type { AuthAttemptLogId } from './AuthAttemptLog';
import type { default as RegSourceEnum } from './RegSourceEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_auditing.registration_log */
export type RegistrationLogId = string & { __brand: 'RegistrationLogId' };

/** Represents the table po105_auditing.registration_log */
export default interface RegistrationLogTable {
  id: ColumnType<RegistrationLogId, RegistrationLogId | undefined, RegistrationLogId>;

  user_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  attempt_id: ColumnType<AuthAttemptLogId | null, AuthAttemptLogId | null, AuthAttemptLogId | null>;

  reg_source: ColumnType<RegSourceEnum, RegSourceEnum, RegSourceEnum>;

  reg_source_other: ColumnType<string | null, string | null, string | null>;

  device_uid: ColumnType<string | null, string | null, string | null>;

  device_type: ColumnType<string | null, string | null, string | null>;

  device_model: ColumnType<string | null, string | null, string | null>;

  device_model_id: ColumnType<string | null, string | null, string | null>;

  os_version: ColumnType<string | null, string | null, string | null>;

  ip_address: ColumnType<string | null, string | null, string | null>;

  app_version: ColumnType<string | null, string | null, string | null>;

  additional_data: ColumnType<unknown | null, unknown | null, unknown | null>;

  reg_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type RegistrationLog = Selectable<RegistrationLogTable>;

export type NewRegistrationLog = Insertable<RegistrationLogTable>;

export type RegistrationLogUpdate = Updateable<RegistrationLogTable>;
