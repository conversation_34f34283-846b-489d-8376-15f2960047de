// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutId } from '../po105_job/Callout';
import type { ItemCostId } from './ItemCost';
import type { default as ChargeTypeEnum } from './ChargeTypeEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_billing.charge_request */
export type ChargeRequestId = string & { __brand: 'ChargeRequestId' };

/** Represents the table po105_billing.charge_request */
export default interface ChargeRequestTable {
  id: ColumnType<ChargeRequestId, ChargeRequestId | undefined, ChargeRequestId>;

  callout_id: ColumnType<CalloutId | null, CalloutId | null, CalloutId | null>;

  request_timestamp: ColumnType<Date, Date | string, Date | string>;

  item_cost_id: ColumnType<ItemCostId, ItemCostId, ItemCostId>;

  charge_type: ColumnType<ChargeTypeEnum, ChargeTypeEnum, ChargeTypeEnum>;

  type_other: ColumnType<string | null, string | null, string | null>;

  description: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type ChargeRequest = Selectable<ChargeRequestTable>;

export type NewChargeRequest = Insertable<ChargeRequestTable>;

export type ChargeRequestUpdate = Updateable<ChargeRequestTable>;
