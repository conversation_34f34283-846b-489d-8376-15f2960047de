// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutId } from '../po105_job/Callout';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_billing.customer_invoice */
export type CustomerInvoiceId = string & { __brand: 'CustomerInvoiceId' };

/** Represents the table po105_billing.customer_invoice */
export default interface CustomerInvoiceTable {
  id: ColumnType<CustomerInvoiceId, CustomerInvoiceId | undefined, CustomerInvoiceId>;

  callout_id: ColumnType<CalloutId, CalloutId, CalloutId>;

  invoice_timestamp: ColumnType<Date, Date | string, Date | string>;

  total_amount: ColumnType<string, string, string>;

  payment_max_date: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CustomerInvoice = Selectable<CustomerInvoiceTable>;

export type NewCustomerInvoice = Insertable<CustomerInvoiceTable>;

export type CustomerInvoiceUpdate = Updateable<CustomerInvoiceTable>;
