// @generated
// This file is automatically generated by Kanel. Do not modify manually.

import type { default as CustomerInvoiceTable } from './CustomerInvoice';
import type { default as ChargeResponseTable } from './ChargeResponse';
import type { default as ChargeRequestTable } from './ChargeRequest';
import type { default as ItemCostTable } from './ItemCost';

export default interface Po105BillingSchema {
  'po105_billing.customer_invoice': CustomerInvoiceTable;

  'po105_billing.charge_response': ChargeResponseTable;

  'po105_billing.charge_request': ChargeRequestTable;

  'po105_billing.item_cost': ItemCostTable;
}
