// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from './File';
import type { default as VideoMimeTypeEnum } from './VideoMimeTypeEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_storage.video */
export default interface VideoTable {
  file_id: ColumnType<FileId, FileId, FileId>;

  duration: ColumnType<string, string, string>;

  mime_type: ColumnType<VideoMimeTypeEnum, VideoMimeTypeEnum, VideoMimeTypeEnum>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Video = Selectable<VideoTable>;

export type NewVideo = Insertable<VideoTable>;

export type VideoUpdate = Updateable<VideoTable>;
