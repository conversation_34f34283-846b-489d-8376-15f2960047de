// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from './File';
import type { default as DocumentMimeTypeEnum } from './DocumentMimeTypeEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_storage.document */
export default interface DocumentTable {
  file_id: ColumnType<FileId, FileId, FileId>;

  mime_type: ColumnType<DocumentMimeTypeEnum, DocumentMimeTypeEnum, DocumentMimeTypeEnum>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Document = Selectable<DocumentTable>;

export type NewDocument = Insertable<DocumentTable>;

export type DocumentUpdate = Updateable<DocumentTable>;
