// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as FileTypeEnum } from './FileTypeEnum';
import type { UserId } from '../po105_core/User';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_storage.file */
export type FileId = string & { __brand: 'FileId' };

/** Represents the table po105_storage.file */
export default interface FileTable {
  id: ColumnType<FileId, FileId | undefined, FileId>;

  file_type: ColumnType<FileTypeEnum, FileTypeEnum, FileTypeEnum>;

  file_name: ColumnType<string, string, string>;

  file_extension: ColumnType<string, string, string>;

  file_size: ColumnType<string, string, string>;

  description: ColumnType<string | null, string | null, string | null>;

  upload_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  uploader_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  bucket_name: ColumnType<string, string | undefined, string>;

  bucket_id: ColumnType<string, string | undefined, string>;

  owner_id: ColumnType<string, string, string>;

  metadata: ColumnType<unknown | null, unknown | null, unknown | null>;

  tags: ColumnType<string[] | null, string[] | null, string[] | null>;
}

export type File = Selectable<FileTable>;

export type NewFile = Insertable<FileTable>;

export type FileUpdate = Updateable<FileTable>;
