// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from './File';
import type { default as ImageMimeTypeEnum } from './ImageMimeTypeEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_storage.image */
export default interface ImageTable {
  file_id: ColumnType<FileId, FileId, FileId>;

  mime_type: ColumnType<ImageMimeTypeEnum, ImageMimeTypeEnum, ImageMimeTypeEnum>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Image = Selectable<ImageTable>;

export type NewImage = Insertable<ImageTable>;

export type ImageUpdate = Updateable<ImageTable>;
