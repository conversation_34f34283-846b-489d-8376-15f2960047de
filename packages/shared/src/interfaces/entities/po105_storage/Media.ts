// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from './File';
import type { default as MediaSourceEnum } from './MediaSourceEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_storage.media */
export default interface MediaTable {
  file_id: ColumnType<FileId, FileId, FileId>;

  width: ColumnType<number, number, number>;

  height: ColumnType<number, number, number>;

  source: ColumnType<MediaSourceEnum, MediaSourceEnum, MediaSourceEnum>;

  capture_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Media = Selectable<MediaTable>;

export type NewMedia = Insertable<MediaTable>;

export type MediaUpdate = Updateable<MediaTable>;
