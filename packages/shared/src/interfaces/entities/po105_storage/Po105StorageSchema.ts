// @generated
// This file is automatically generated by Kanel. Do not modify manually.

import type { default as MediaTable } from './Media';
import type { default as FileTable } from './File';
import type { default as AudioTable } from './Audio';
import type { default as DocumentTable } from './Document';
import type { default as ImageTable } from './Image';
import type { default as VideoTable } from './Video';

export default interface Po105StorageSchema {
  'po105_storage.media': MediaTable;

  'po105_storage.file': FileTable;

  'po105_storage.audio': AudioTable;

  'po105_storage.document': DocumentTable;

  'po105_storage.image': ImageTable;

  'po105_storage.video': VideoTable;
}
