// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from './File';
import type { default as AudioMimeTypeEnum } from './AudioMimeTypeEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_storage.audio */
export default interface AudioTable {
  file_id: ColumnType<FileId, FileId, FileId>;

  duration: ColumnType<string, string, string>;

  mime_type: ColumnType<AudioMimeTypeEnum, AudioMimeTypeEnum, AudioMimeTypeEnum>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Audio = Selectable<AudioTable>;

export type NewAudio = Insertable<AudioTable>;

export type AudioUpdate = Updateable<AudioTable>;
