// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { SiteId } from '../po105_management/Site';
import type { default as WorkTypeEnum } from './WorkTypeEnum';
import type { UserId } from '../po105_core/User';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.work_order */
export type WorkOrderId = string & { __brand: 'WorkOrderId' };

/** Represents the table po105_job.work_order */
export default interface WorkOrderTable {
  id: ColumnType<WorkOrderId, WorkOrderId | undefined, WorkOrderId>;

  site_id: ColumnType<SiteId | null, SiteId | null, SiteId | null>;

  work_type: ColumnType<WorkTypeEnum, WorkTypeEnum, WorkTypeEnum>;

  additional_attributes: ColumnType<unknown | null, unknown | null, unknown | null>;

  declaration_timestamp: ColumnType<Date, Date | string, Date | string>;

  site_manager_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type WorkOrder = Selectable<WorkOrderTable>;

export type NewWorkOrder = Insertable<WorkOrderTable>;

export type WorkOrderUpdate = Updateable<WorkOrderTable>;
