// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutRequestId } from './CalloutRequest';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.callout */
export type CalloutId = string & { __brand: 'CalloutId' };

/** Represents the table po105_job.callout */
export default interface CalloutTable {
  id: ColumnType<CalloutId, CalloutId | undefined, CalloutId>;

  callout_request_id: ColumnType<CalloutRequestId | null, CalloutRequestId | null, CalloutRequestId | null>;

  callout_start_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Callout = Selectable<CalloutTable>;

export type NewCallout = Insertable<CalloutTable>;

export type CalloutUpdate = Updateable<CalloutTable>;
