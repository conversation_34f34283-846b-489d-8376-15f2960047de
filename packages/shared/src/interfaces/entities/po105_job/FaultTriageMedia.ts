// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FaultTriageId } from './FaultTriage';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_job.fault_triage_media */
export default interface FaultTriageMediaTable {
  fault_triage_id: ColumnType<FaultTriageId, FaultTriageId, FaultTriageId>;

  media_file_id: ColumnType<FileId, FileId, FileId>;

  caption: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type FaultTriageMedia = Selectable<FaultTriageMediaTable>;

export type NewFaultTriageMedia = Insertable<FaultTriageMediaTable>;

export type FaultTriageMediaUpdate = Updateable<FaultTriageMediaTable>;
