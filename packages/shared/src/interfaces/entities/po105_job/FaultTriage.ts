// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { WorkOrderId } from './WorkOrder';
import type { default as FaultTypeEnum } from './FaultTypeEnum';
import type { SubstationId } from '../po105_management/Substation';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.fault_triage */
export type FaultTriageId = string & { __brand: 'FaultTriageId' };

/** Represents the table po105_job.fault_triage */
export default interface FaultTriageTable {
  id: ColumnType<FaultTriageId, FaultTriageId | undefined, FaultTriageId>;

  work_order_id: ColumnType<WorkOrderId | null, WorkOrderId | null, WorkOrderId | null>;

  fault_type: ColumnType<FaultTypeEnum, FaultTypeEnum, FaultTypeEnum>;

  fault_type_other: ColumnType<string | null, string | null, string | null>;

  severity: ColumnType<number | null, number | null, number | null>;

  priority: ColumnType<number | null, number | null, number | null>;

  customer_description: ColumnType<string, string, string>;

  affected_substation: ColumnType<SubstationId | null, SubstationId | null, SubstationId | null>;

  automated_description: ColumnType<string | null, string | null, string | null>;

  additional_attributes: ColumnType<unknown | null, unknown | null, unknown | null>;

  submission_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type FaultTriage = Selectable<FaultTriageTable>;

export type NewFaultTriage = Insertable<FaultTriageTable>;

export type FaultTriageUpdate = Updateable<FaultTriageTable>;
