// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutRequestId } from './CalloutRequest';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.callout_response */
export type CalloutResponseId = string & { __brand: 'CalloutResponseId' };

/** Represents the table po105_job.callout_response */
export default interface CalloutResponseTable {
  id: ColumnType<CalloutResponseId, CalloutResponseId | undefined, CalloutResponseId>;

  callout_request_id: ColumnType<CalloutRequestId, CalloutRequestId, CalloutRequestId>;

  request_receipt_timestamp: ColumnType<Date, Date | string, Date | string>;

  response: ColumnType<boolean, boolean, boolean>;

  response_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CalloutResponse = Selectable<CalloutResponseTable>;

export type NewCalloutResponse = Insertable<CalloutResponseTable>;

export type CalloutResponseUpdate = Updateable<CalloutResponseTable>;
