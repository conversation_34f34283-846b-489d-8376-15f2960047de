// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from '../po105_core/User';
import type { WorkOrderId } from './WorkOrder';
import type { ItemCostId } from '../po105_billing/ItemCost';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.callout_request */
export type CalloutRequestId = string & { __brand: 'CalloutRequestId' };

/** Represents the table po105_job.callout_request */
export default interface CalloutRequestTable {
  id: ColumnType<CalloutRequestId, CalloutRequestId | undefined, CalloutRequestId>;

  site_manager_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  sap_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  work_order_id: ColumnType<WorkOrderId, WorkOrderId, WorkOrderId>;

  issuance_timestamp: ColumnType<Date, Date | string, Date | string>;

  expires_after: ColumnType<string, string, string>;

  estimated_distance_miles: ColumnType<string, string, string>;

  estimated_time_mins: ColumnType<number, number, number>;

  estimated_initial_cost_id: ColumnType<ItemCostId | null, ItemCostId | null, ItemCostId | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CalloutRequest = Selectable<CalloutRequestTable>;

export type NewCalloutRequest = Insertable<CalloutRequestTable>;

export type CalloutRequestUpdate = Updateable<CalloutRequestTable>;
