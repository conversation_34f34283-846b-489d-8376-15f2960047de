// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutId } from './Callout';
import type { InstalledEquipmentId } from '../po105_management/InstalledEquipment';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.callout_equipment */
export type CalloutEquipmentId = string & { __brand: 'CalloutEquipmentId' };

/** Represents the table po105_job.callout_equipment */
export default interface CalloutEquipmentTable {
  id: ColumnType<CalloutEquipmentId, CalloutEquipmentId | undefined, CalloutEquipmentId>;

  callout_id: ColumnType<CalloutId, CalloutId, CalloutId>;

  installed_equipment_id: ColumnType<InstalledEquipmentId | null, InstalledEquipmentId | null, InstalledEquipmentId | null>;

  summary: ColumnType<string, string, string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CalloutEquipment = Selectable<CalloutEquipmentTable>;

export type NewCalloutEquipment = Insertable<CalloutEquipmentTable>;

export type CalloutEquipmentUpdate = Updateable<CalloutEquipmentTable>;
