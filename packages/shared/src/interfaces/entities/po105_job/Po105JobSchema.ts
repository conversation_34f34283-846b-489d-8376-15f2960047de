// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as CalloutRequestTable } from './CalloutRequest';
import type { default as FaultTriageTable } from './FaultTriage';
import type { default as FaultTriageMediaTable } from './FaultTriageMedia';
import type { default as CalloutCompletionTable } from './CalloutCompletion';
import type { default as CalloutEquipmentTable } from './CalloutEquipment';
import type { default as CalloutEquipmentMediaTable } from './CalloutEquipmentMedia';
import type { default as CalloutTable } from './Callout';
import type { default as CalloutResponseTable } from './CalloutResponse';
import type { default as WorkOrderTable } from './WorkOrder';

export default interface Po105JobSchema {
  'po105_job.callout_request': CalloutRequestTable;

  'po105_job.fault_triage': FaultTriageTable;

  'po105_job.fault_triage_media': FaultTriageMediaTable;

  'po105_job.callout_completion': CalloutCompletionTable;

  'po105_job.callout_equipment': CalloutEquipmentTable;

  'po105_job.callout_equipment_media': CalloutEquipmentMediaTable;

  'po105_job.callout': CalloutTable;

  'po105_job.callout_response': CalloutResponseTable;

  'po105_job.work_order': WorkOrderTable;
}
