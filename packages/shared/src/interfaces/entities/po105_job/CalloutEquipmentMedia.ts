// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutEquipmentId } from './CalloutEquipment';
import type { FileId } from '../po105_storage/File';
import type { default as CalloutStageEnum } from './CalloutStageEnum';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_job.callout_equipment_media */
export default interface CalloutEquipmentMediaTable {
  callout_equipment_id: ColumnType<CalloutEquipmentId, CalloutEquipmentId, CalloutEquipmentId>;

  media_file_id: ColumnType<FileId, FileId, FileId>;

  callout_stage: ColumnType<CalloutStageEnum, CalloutStageEnum, CalloutStageEnum>;

  callout_stage_other: ColumnType<string | null, string | null, string | null>;

  description: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CalloutEquipmentMedia = Selectable<CalloutEquipmentMediaTable>;

export type NewCalloutEquipmentMedia = Insertable<CalloutEquipmentMediaTable>;

export type CalloutEquipmentMediaUpdate = Updateable<CalloutEquipmentMediaTable>;
