// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CalloutId } from './Callout';
import type { default as CompletionTriggerEnum } from './CompletionTriggerEnum';
import type { ItemCostId } from '../po105_billing/ItemCost';
import type { UserFeedbackId } from '../po105_core/UserFeedback';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_job.callout_completion */
export type CalloutCompletionId = string & { __brand: 'CalloutCompletionId' };

/** Represents the table po105_job.callout_completion */
export default interface CalloutCompletionTable {
  id: ColumnType<CalloutCompletionId, CalloutCompletionId | undefined, CalloutCompletionId>;

  callout_id: ColumnType<CalloutId, CalloutId, CalloutId>;

  completion_timestamp: ColumnType<Date, Date | string, Date | string>;

  completion_trigger: ColumnType<CompletionTriggerEnum | null, CompletionTriggerEnum | null, CompletionTriggerEnum | null>;

  completion_trigger_other: ColumnType<string | null, string | null, string | null>;

  summary: ColumnType<string, string, string>;

  opening_cost_id: ColumnType<ItemCostId, ItemCostId, ItemCostId>;

  travel_cost_id: ColumnType<ItemCostId, ItemCostId, ItemCostId>;

  time_cost_id: ColumnType<ItemCostId, ItemCostId, ItemCostId>;

  sap_feedback_id: ColumnType<UserFeedbackId | null, UserFeedbackId | null, UserFeedbackId | null>;

  customer_feedback_id: ColumnType<UserFeedbackId | null, UserFeedbackId | null, UserFeedbackId | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type CalloutCompletion = Selectable<CalloutCompletionTable>;

export type NewCalloutCompletion = Insertable<CalloutCompletionTable>;

export type CalloutCompletionUpdate = Updateable<CalloutCompletionTable>;
