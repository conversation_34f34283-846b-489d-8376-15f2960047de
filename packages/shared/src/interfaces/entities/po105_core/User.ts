// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as UserTypeEnum } from './UserTypeEnum';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_core.user */
export type UserId = string & { __brand: 'UserId' };

/** Represents the table po105_core.user */
export default interface UserTable {
  id: ColumnType<UserId, UserId | undefined, UserId>;

  user_type: ColumnType<UserTypeEnum, UserTypeEnum, UserTypeEnum>;

  email: ColumnType<string, string, string>;

  first_name: ColumnType<string, string, string>;

  last_name: ColumnType<string, string, string>;

  phone_number: ColumnType<string, string, string>;

  registration_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  force_password_change: ColumnType<boolean, boolean | undefined, boolean>;

  average_rating: ColumnType<string | null, string | null, string | null>;

  total_ratings: ColumnType<number | null, number | null, number | null>;

  profile_image_id: ColumnType<FileId | null, FileId | null, FileId | null>;
}

export type User = Selectable<UserTable>;

export type NewUser = Insertable<UserTable>;

export type UserUpdate = Updateable<UserTable>;
