// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as MsgTypeEnum } from './MsgTypeEnum';
import type { UserId } from './User';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_core.message */
export type MessageId = string & { __brand: 'MessageId' };

/** Represents the table po105_core.message */
export default interface MessageTable {
  id: ColumnType<MessageId, MessageId | undefined, MessageId>;

  msg_type: ColumnType<MsgTypeEnum, MsgTypeEnum, MsgTypeEnum>;

  sender_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  recipient_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  content: ColumnType<string | null, string | null, string | null>;

  file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  msg_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Message = Selectable<MessageTable>;

export type NewMessage = Insertable<MessageTable>;

export type MessageUpdate = Updateable<MessageTable>;
