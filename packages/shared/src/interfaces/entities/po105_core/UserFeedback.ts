// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from './User';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_core.user_feedback */
export type UserFeedbackId = string & { __brand: 'UserFeedbackId' };

/** Represents the table po105_core.user_feedback */
export default interface UserFeedbackTable {
  id: ColumnType<UserFeedbackId, UserFeedbackId | undefined, UserFeedbackId>;

  sender_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  recipient_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  rating: ColumnType<number, number, number>;

  review: ColumnType<string | null, string | null, string | null>;

  feedback_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type UserFeedback = Selectable<UserFeedbackTable>;

export type NewUserFeedback = Insertable<UserFeedbackTable>;

export type UserFeedbackUpdate = Updateable<UserFeedbackTable>;
