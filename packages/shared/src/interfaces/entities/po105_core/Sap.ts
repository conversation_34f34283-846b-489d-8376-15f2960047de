// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from './User';
import type { PartnerId } from '../po105_management/Partner';
import type { PermanentLocationId } from '../po105_management/PermanentLocation';
import type { LiveLocationId } from './LiveLocation';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_core.sap */
export default interface SapTable {
  user_id: ColumnType<UserId, UserId, UserId>;

  qualification: ColumnType<string | null, string | null, string | null>;

  availability: ColumnType<boolean, boolean, boolean>;

  partner_id: ColumnType<PartnerId | null, PartnerId | null, PartnerId | null>;

  perm_location_id: ColumnType<PermanentLocationId | null, PermanentLocationId | null, PermanentLocationId | null>;

  last_known_location_id: ColumnType<LiveLocationId | null, LiveLocationId | null, LiveLocationId | null>;

  sla_file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  sla_additional_attributes: ColumnType<unknown | null, unknown | null, unknown | null>;

  rate_per_mile: ColumnType<string, string, string>;

  rate_per_hour: ColumnType<string, string, string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  total_callouts: ColumnType<number | null, number | null, number | null>;
}

export type Sap = Selectable<SapTable>;

export type NewSap = Insertable<SapTable>;

export type SapUpdate = Updateable<SapTable>;
