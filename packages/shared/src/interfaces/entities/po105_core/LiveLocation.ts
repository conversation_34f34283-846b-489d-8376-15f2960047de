// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_core.live_location */
export type LiveLocationId = string & { __brand: 'LiveLocationId' };

/** Represents the table po105_core.live_location */
export default interface LiveLocationTable {
  id: ColumnType<LiveLocationId, LiveLocationId | undefined, LiveLocationId>;

  geog: ColumnType<import("geojson").Geometry, import("geojson").Geometry, import("geojson").Geometry>;

  gps_accuracy: ColumnType<number | null, number | null, number | null>;

  reading_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type LiveLocation = Selectable<LiveLocationTable>;

export type NewLiveLocation = Insertable<LiveLocationTable>;

export type LiveLocationUpdate = Updateable<LiveLocationTable>;
