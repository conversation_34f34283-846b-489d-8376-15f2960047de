// @generated
// This file is automatically generated by Kanel. Do not modify manually.

import type { default as SapTable } from './Sap';
import type { default as UserTable } from './User';
import type { default as UserFeedbackTable } from './UserFeedback';
import type { default as LiveLocationTable } from './LiveLocation';

export default interface Po105CoreSchema {
  'po105_core.sap': SapTable;

  'po105_core.user': UserTable;

  'po105_core.user_feedback': UserFeedbackTable;

  'po105_core.live_location': LiveLocationTable;
}
