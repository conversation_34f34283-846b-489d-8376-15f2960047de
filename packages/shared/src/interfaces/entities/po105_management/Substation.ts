// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { PermanentLocationId } from './PermanentLocation';
import type { SiteId } from './Site';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.substation */
export type SubstationId = string & { __brand: 'SubstationId' };

/** Represents the table po105_management.substation */
export default interface SubstationTable {
  id: ColumnType<SubstationId, SubstationId | undefined, SubstationId>;

  reference: ColumnType<string | null, string | null, string | null>;

  name: ColumnType<string, string, string>;

  number: ColumnType<string, string, string>;

  perm_location_id: ColumnType<PermanentLocationId | null, PermanentLocationId | null, PermanentLocationId | null>;

  site_id: ColumnType<SiteId, SiteId, SiteId>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Substation = Selectable<SubstationTable>;

export type NewSubstation = Insertable<SubstationTable>;

export type SubstationUpdate = Updateable<SubstationTable>;
