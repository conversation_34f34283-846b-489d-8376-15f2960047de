// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { SopId } from './Sop';
import type { InstalledEquipmentId } from './InstalledEquipment';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.sop_affected_equipment */
export type SopAffectedEquipmentId = string & { __brand: 'SopAffectedEquipmentId' };

/** Represents the table po105_management.sop_affected_equipment */
export default interface SopAffectedEquipmentTable {
  id: ColumnType<SopAffectedEquipmentId, SopAffectedEquipmentId | undefined, SopAffectedEquipmentId>;

  sop_id: ColumnType<SopId, SopId, SopId>;

  installed_equipment_id: ColumnType<InstalledEquipmentId, InstalledEquipmentId, InstalledEquipmentId>;

  affected_area: ColumnType<string | null, string | null, string | null>;

  corrective_action: ColumnType<string | null, string | null, string | null>;

  is_resolved: ColumnType<boolean, boolean, boolean>;

  resolution_date: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type SopAffectedEquipment = Selectable<SopAffectedEquipmentTable>;

export type NewSopAffectedEquipment = Insertable<SopAffectedEquipmentTable>;

export type SopAffectedEquipmentUpdate = Updateable<SopAffectedEquipmentTable>;
