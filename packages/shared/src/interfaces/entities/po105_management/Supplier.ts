// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CompanyId } from './Company';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.supplier */
export type SupplierId = string & { __brand: 'SupplierId' };

/** Represents the table po105_management.supplier */
export default interface SupplierTable {
  id: ColumnType<SupplierId, SupplierId | undefined, SupplierId>;

  company_id: ColumnType<CompanyId, CompanyId, CompanyId>;

  sla_file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  sla_additional_attributes: ColumnType<unknown | null, unknown | null, unknown | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Supplier = Selectable<SupplierTable>;

export type NewSupplier = Insertable<SupplierTable>;

export type SupplierUpdate = Updateable<SupplierTable>;
