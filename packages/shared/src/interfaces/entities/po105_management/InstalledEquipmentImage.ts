// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { InstalledEquipmentId } from './InstalledEquipment';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_management.installed_equipment_image */
export default interface InstalledEquipmentImageTable {
  installed_equipment_id: ColumnType<InstalledEquipmentId, InstalledEquipmentId, InstalledEquipmentId>;

  image_id: ColumnType<FileId, FileId, FileId>;

  description: ColumnType<string | null, string | null, string | null>;

  capture_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type InstalledEquipmentImage = Selectable<InstalledEquipmentImageTable>;

export type NewInstalledEquipmentImage = Insertable<InstalledEquipmentImageTable>;

export type InstalledEquipmentImageUpdate = Updateable<InstalledEquipmentImageTable>;
