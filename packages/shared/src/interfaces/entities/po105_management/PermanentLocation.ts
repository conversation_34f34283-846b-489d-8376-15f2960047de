// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.permanent_location */
export type PermanentLocationId = string & { __brand: 'PermanentLocationId' };

/** Represents the table po105_management.permanent_location */
export default interface PermanentLocationTable {
  id: ColumnType<PermanentLocationId, PermanentLocationId | undefined, PermanentLocationId>;

  geog: ColumnType<import("geojson").Geometry, import("geojson").Geometry, import("geojson").Geometry>;

  w3w: ColumnType<string | null, string | null, string | null>;

  address_line_1: ColumnType<string, string, string>;

  address_line_2: ColumnType<string | null, string | null, string | null>;

  address_line_3: ColumnType<string | null, string | null, string | null>;

  city: ColumnType<string, string, string>;

  country: ColumnType<string, string, string>;

  postcode: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type PermanentLocation = Selectable<PermanentLocationTable>;

export type NewPermanentLocation = Insertable<PermanentLocationTable>;

export type PermanentLocationUpdate = Updateable<PermanentLocationTable>;
