// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { StockListingId } from './StockListing';
import type { UserId } from '../po105_core/User';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.stock_reservation_request */
export type StockReservationRequestId = string & { __brand: 'StockReservationRequestId' };

/** Represents the table po105_management.stock_reservation_request */
export default interface StockReservationRequestTable {
  id: ColumnType<StockReservationRequestId, StockReservationRequestId | undefined, StockReservationRequestId>;

  stock_listing_id: ColumnType<StockListingId, StockListingId, StockListingId>;

  user_id: ColumnType<UserId, UserId, UserId>;

  request_timestamp: ColumnType<Date, Date | string, Date | string>;

  reservation_duration: ColumnType<string, string, string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type StockReservationRequest = Selectable<StockReservationRequestTable>;

export type NewStockReservationRequest = Insertable<StockReservationRequestTable>;

export type StockReservationRequestUpdate = Updateable<StockReservationRequestTable>;
