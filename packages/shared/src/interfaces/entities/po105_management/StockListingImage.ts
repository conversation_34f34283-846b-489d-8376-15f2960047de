// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { StockListingId } from './StockListing';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_management.stock_listing_image */
export default interface StockListingImageTable {
  stock_listing_id: ColumnType<StockListingId, StockListingId, StockListingId>;

  image_id: ColumnType<FileId, FileId, FileId>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type StockListingImage = Selectable<StockListingImageTable>;

export type NewStockListingImage = Insertable<StockListingImageTable>;

export type StockListingImageUpdate = Updateable<StockListingImageTable>;
