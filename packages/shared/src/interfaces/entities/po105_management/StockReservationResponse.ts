// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { StockReservationRequestId } from './StockReservationRequest';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.stock_reservation_response */
export type StockReservationResponseId = string & { __brand: 'StockReservationResponseId' };

/** Represents the table po105_management.stock_reservation_response */
export default interface StockReservationResponseTable {
  id: ColumnType<StockReservationResponseId, StockReservationResponseId | undefined, StockReservationResponseId>;

  stock_reservation_id: ColumnType<StockReservationRequestId, StockReservationRequestId, StockReservationRequestId>;

  reservation_accepted: ColumnType<boolean, boolean, boolean>;

  response_timestamp: ColumnType<Date, Date | string, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type StockReservationResponse = Selectable<StockReservationResponseTable>;

export type NewStockReservationResponse = Insertable<StockReservationResponseTable>;

export type StockReservationResponseUpdate = Updateable<StockReservationResponseTable>;
