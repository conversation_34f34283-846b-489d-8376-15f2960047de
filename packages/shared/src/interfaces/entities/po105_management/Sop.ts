// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.sop */
export type SopId = string & { __brand: 'SopId' };

/** Represents the table po105_management.sop */
export default interface SopTable {
  id: ColumnType<SopId, SopId | undefined, SopId>;

  pdf_file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  issue_date: ColumnType<Date, Date | string, Date | string>;

  issue_reference: ColumnType<string, string, string>;

  issue_contact_info: ColumnType<unknown | null, unknown | null, unknown | null>;

  originating_company: ColumnType<string | null, string | null, string | null>;

  cause: ColumnType<string, string, string>;

  nature_of_restriction: ColumnType<string, string, string>;

  substation_accessible: ColumnType<boolean, boolean, boolean>;

  equipment_type: ColumnType<string[], string[], string[]>;

  additional_comments: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Sop = Selectable<SopTable>;

export type NewSop = Insertable<SopTable>;

export type SopUpdate = Updateable<SopTable>;
