// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { EquipmentId } from './Equipment';
import type { SubstationId } from './Substation';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.installed_equipment */
export type InstalledEquipmentId = string & { __brand: 'InstalledEquipmentId' };

/** Represents the table po105_management.installed_equipment */
export default interface InstalledEquipmentTable {
  id: ColumnType<InstalledEquipmentId, InstalledEquipmentId | undefined, InstalledEquipmentId>;

  equipment_id: ColumnType<EquipmentId, EquipmentId, EquipmentId>;

  substation_id: ColumnType<SubstationId, SubstationId, SubstationId>;

  placement: ColumnType<string, string, string>;

  commissioning_date: ColumnType<Date | null, Date | string | null, Date | string | null>;

  last_inspection_date: ColumnType<Date | null, Date | string | null, Date | string | null>;

  serial_number: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type InstalledEquipment = Selectable<InstalledEquipmentTable>;

export type NewInstalledEquipment = Insertable<InstalledEquipmentTable>;

export type InstalledEquipmentUpdate = Updateable<InstalledEquipmentTable>;
