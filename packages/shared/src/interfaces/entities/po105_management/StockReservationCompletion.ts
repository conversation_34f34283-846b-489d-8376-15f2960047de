// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { StockReservationRequestId } from './StockReservationRequest';
import type { UserId } from '../po105_core/User';
import type { UserFeedbackId } from '../po105_core/UserFeedback';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.stock_reservation_completion */
export type StockReservationCompletionId = string & { __brand: 'StockReservationCompletionId' };

/** Represents the table po105_management.stock_reservation_completion */
export default interface StockReservationCompletionTable {
  id: ColumnType<StockReservationCompletionId, StockReservationCompletionId | undefined, StockReservationCompletionId>;

  stock_reservation_id: ColumnType<StockReservationRequestId | null, StockReservationRequestId | null, StockReservationRequestId | null>;

  triggered_by_user_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  reason: ColumnType<string | null, string | null, string | null>;

  reason_other: ColumnType<string | null, string | null, string | null>;

  completion_timestamp: ColumnType<Date | null, Date | string | null, Date | string | null>;

  supplier_feedback_id: ColumnType<UserFeedbackId | null, UserFeedbackId | null, UserFeedbackId | null>;

  recipient_feedback_id: ColumnType<UserFeedbackId | null, UserFeedbackId | null, UserFeedbackId | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type StockReservationCompletion = Selectable<StockReservationCompletionTable>;

export type NewStockReservationCompletion = Insertable<StockReservationCompletionTable>;

export type StockReservationCompletionUpdate = Updateable<StockReservationCompletionTable>;
