// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CustomerId } from './Customer';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.site */
export type SiteId = string & { __brand: 'SiteId' };

/** Represents the table po105_management.site */
export default interface SiteTable {
  id: ColumnType<SiteId, SiteId | undefined, SiteId>;

  reference: ColumnType<string | null, string | null, string | null>;

  name: ColumnType<string, string, string>;

  customer_id: ColumnType<CustomerId, CustomerId, CustomerId>;

  cpl_spreadsheet_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  cpl_json: ColumnType<unknown | null, unknown | null, unknown | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Site = Selectable<SiteTable>;

export type NewSite = Insertable<SiteTable>;

export type SiteUpdate = Updateable<SiteTable>;
