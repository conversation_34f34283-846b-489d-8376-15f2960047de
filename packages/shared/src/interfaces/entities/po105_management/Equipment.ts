// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as EquipmentCategoryEnum } from './EquipmentCategoryEnum';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.equipment */
export type EquipmentId = string & { __brand: 'EquipmentId' };

/** Represents the table po105_management.equipment */
export default interface EquipmentTable {
  id: ColumnType<EquipmentId, EquipmentId | undefined, EquipmentId>;

  category: ColumnType<EquipmentCategoryEnum, EquipmentCategoryEnum, EquipmentCategoryEnum>;

  category_other: ColumnType<string | null, string | null, string | null>;

  reference: ColumnType<string | null, string | null, string | null>;

  manufacturer: ColumnType<string, string, string>;

  eq_type: ColumnType<string, string, string>;

  manufacturing_date: ColumnType<Date | null, Date | string | null, Date | string | null>;

  system_voltage: ColumnType<string | null, string | null, string | null>;

  current_rating: ColumnType<string | null, string | null, string | null>;

  spec_file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  additional_attributes: ColumnType<unknown | null, unknown | null, unknown | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Equipment = Selectable<EquipmentTable>;

export type NewEquipment = Insertable<EquipmentTable>;

export type EquipmentUpdate = Updateable<EquipmentTable>;
