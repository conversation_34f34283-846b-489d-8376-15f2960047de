// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from '../po105_core/User';
import type { CustomerId } from './Customer';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Represents the table po105_management.site_manager */
export default interface SiteManagerTable {
  user_id: ColumnType<UserId, UserId, UserId>;

  customer_id: ColumnType<CustomerId, CustomerId, CustomerId>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type SiteManager = Selectable<SiteManagerTable>;

export type NewSiteManager = Insertable<SiteManagerTable>;

export type SiteManagerUpdate = Updateable<SiteManagerTable>;
