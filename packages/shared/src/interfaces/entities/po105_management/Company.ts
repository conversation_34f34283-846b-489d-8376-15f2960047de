// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { UserId } from '../po105_core/User';
import type { PermanentLocationId } from './PermanentLocation';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.company */
export type CompanyId = string & { __brand: 'CompanyId' };

/** Represents the table po105_management.company */
export default interface CompanyTable {
  id: ColumnType<CompanyId, CompanyId | undefined, CompanyId>;

  company_type: ColumnType<string, string, string>;

  internal_ref: ColumnType<string, string, string>;

  name: ColumnType<string, string, string>;

  contact_email: ColumnType<string, string, string>;

  phone_number: ColumnType<string | null, string | null, string | null>;

  account_manager_id: ColumnType<UserId | null, UserId | null, UserId | null>;

  perm_location_id: ColumnType<PermanentLocationId | null, PermanentLocationId | null, PermanentLocationId | null>;

  profile_image_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  company_reg: ColumnType<string | null, string | null, string | null>;

  vat_number: ColumnType<string | null, string | null, string | null>;

  internal_notes: ColumnType<string[] | null, string[] | null, string[] | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Company = Selectable<CompanyTable>;

export type NewCompany = Insertable<CompanyTable>;

export type CompanyUpdate = Updateable<CompanyTable>;
