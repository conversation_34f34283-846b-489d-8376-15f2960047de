// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { SupplierId } from './Supplier';
import type { EquipmentId } from './Equipment';
import type { ItemCostId } from '../po105_billing/ItemCost';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.stock_listing */
export type StockListingId = string & { __brand: 'StockListingId' };

/** Represents the table po105_management.stock_listing */
export default interface StockListingTable {
  id: ColumnType<StockListingId, StockListingId | undefined, StockListingId>;

  supplier_id: ColumnType<SupplierId, SupplierId, SupplierId>;

  equipment_id: ColumnType<EquipmentId, EquipmentId, EquipmentId>;

  quantity: ColumnType<number, number, number>;

  item_cost_id: ColumnType<ItemCostId, ItemCostId, ItemCostId>;

  listing_timestamp: ColumnType<Date, Date | string | undefined, Date | string>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type StockListing = Selectable<StockListingTable>;

export type NewStockListing = Insertable<StockListingTable>;

export type StockListingUpdate = Updateable<StockListingTable>;
