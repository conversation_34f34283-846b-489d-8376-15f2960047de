// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { CompanyId } from './Company';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.customer */
export type CustomerId = string & { __brand: 'CustomerId' };

/** Represents the table po105_management.customer */
export default interface CustomerTable {
  id: ColumnType<CustomerId, CustomerId | undefined, CustomerId>;

  company_id: ColumnType<CompanyId, CompanyId, CompanyId>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Customer = Selectable<CustomerTable>;

export type NewCustomer = Insertable<CustomerTable>;

export type CustomerUpdate = Updateable<CustomerTable>;
