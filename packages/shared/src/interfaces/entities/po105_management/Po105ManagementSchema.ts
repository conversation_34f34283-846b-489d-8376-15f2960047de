// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { default as SopTable } from './Sop';
import type { default as SopAffectedEquipmentTable } from './SopAffectedEquipment';
import type { default as PartnerTable } from './Partner';
import type { default as SiteManagerTable } from './SiteManager';
import type { default as SubstationTable } from './Substation';
import type { default as CustomerTable } from './Customer';
import type { default as InstalledEquipmentTable } from './InstalledEquipment';
import type { default as StockReservationResponseTable } from './StockReservationResponse';
import type { default as PermanentLocationTable } from './PermanentLocation';
import type { default as StockReservationRequestTable } from './StockReservationRequest';
import type { default as SupplierTable } from './Supplier';
import type { default as StockReservationCompletionTable } from './StockReservationCompletion';
import type { default as StockListingImageTable } from './StockListingImage';
import type { default as StockListingTable } from './StockListing';
import type { default as EquipmentTable } from './Equipment';
import type { default as InstalledEquipmentImageTable } from './InstalledEquipmentImage';
import type { default as SiteTable } from './Site';
import type { default as CompanyTable } from './Company';
import type { default as DinTable } from './Din';

export default interface Po105ManagementSchema {
  'po105_management.sop': SopTable;

  'po105_management.sop_affected_equipment': SopAffectedEquipmentTable;

  'po105_management.partner': PartnerTable;

  'po105_management.site_manager': SiteManagerTable;

  'po105_management.substation': SubstationTable;

  'po105_management.customer': CustomerTable;

  'po105_management.installed_equipment': InstalledEquipmentTable;

  'po105_management.stock_reservation_response': StockReservationResponseTable;

  'po105_management.permanent_location': PermanentLocationTable;

  'po105_management.stock_reservation_request': StockReservationRequestTable;

  'po105_management.supplier': SupplierTable;

  'po105_management.stock_reservation_completion': StockReservationCompletionTable;

  'po105_management.stock_listing_image': StockListingImageTable;

  'po105_management.stock_listing': StockListingTable;

  'po105_management.equipment': EquipmentTable;

  'po105_management.installed_equipment_image': InstalledEquipmentImageTable;

  'po105_management.site': SiteTable;

  'po105_management.company': CompanyTable;

  'po105_management.din': DinTable;
}
