// @generated
// This file is automatically generated by <PERSON><PERSON>. Do not modify manually.

import type { InstalledEquipmentId } from './InstalledEquipment';
import type { FileId } from '../po105_storage/File';
import type { ColumnType, Selectable, Insertable, Updateable } from 'kysely';

/** Identifier type for po105_management.din */
export type DinId = string & { __brand: 'DinId' };

/** Represents the table po105_management.din */
export default interface DinTable {
  id: ColumnType<DinId, DinId | undefined, DinId>;

  installed_equipment_id: ColumnType<InstalledEquipmentId | null, InstalledEquipmentId | null, InstalledEquipmentId | null>;

  pdf_file_id: ColumnType<FileId | null, FileId | null, FileId | null>;

  issue_contact_info: ColumnType<unknown | null, unknown | null, unknown | null>;

  issue_date: ColumnType<Date, Date | string, Date | string>;

  issue_reference: ColumnType<string | null, string | null, string | null>;

  description: ColumnType<string, string, string>;

  failure_areas: ColumnType<string[], string[], string[]>;

  causes: ColumnType<string[], string[], string[]>;

  operating_environment: ColumnType<string | null, string | null, string | null>;

  incident_date: ColumnType<Date, Date | string, Date | string>;

  linked_incident_ref: ColumnType<string | null, string | null, string | null>;

  created_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  updated_at: ColumnType<Date | null, Date | string | null, Date | string | null>;

  deleted_at: ColumnType<Date | null, Date | string | null, Date | string | null>;
}

export type Din = Selectable<DinTable>;

export type NewDin = Insertable<DinTable>;

export type DinUpdate = Updateable<DinTable>;
