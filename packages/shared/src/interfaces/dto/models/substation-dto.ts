/**
 * DTO for a Substations in a Customer's Site
 */

/* IMPORTS ================================================================= */

import Substation from "@interfaces/entities/po105_management/Substation";
import { PermanentLocationDto } from "./location-dto";
import { EquipmentDto } from "./equipment-dto";

/* INTERFACES ============================================================== */
/**
 * Base substation information including ID, reference, name and number
 */
type BaseSubstation = Pick<Substation, "id" | "reference" | "name" | "number">;

/**
 * Substation data transfer object with location and equipment information
 */
export interface SubstationDto extends BaseSubstation {
  /** Permanent location information for the substation */
  perm_location: PermanentLocationDto;
  /** List of equipment installed at this substation */
  equipment: EquipmentDto[];
}
