/**
 * DTO for SAPs
 */

/* IMPORTS ================================================================= */

import { UserProfileDto } from "./user-dto";
import { CompanyProfileDto } from "./comapny-dto";
import { Sap } from "@interfaces/entities/po105_core/Sap";
import { DocumentDto } from "./file-dto";
import { PermanentLocationDto } from "./location-dto";

/* INTERFACES ============================================================== */

/**
 * Base SAP information including qualification and availability status
 */
type BaseSap = Pick<Sap, "qualification" | "availability" | "total_callouts">;

/**
 * Senior Authorised Person (SAP) profile with qualification details and partner information
 */
export interface SapDto extends UserProfileDto, BaseSap {
  /** Partner company information and SLA details */
  partner?: {
    /** Company profile information */
    company: CompanyProfileDto;
    /** Service Level Agreement document for the partner */
    sla_file?: DocumentDto;
  };
  /** SAP's individual Service Level Agreement document */
  sla_file?: DocumentDto;
  /** SAP's permanent location information */
  permanent_location?: PermanentLocationDto;
}
