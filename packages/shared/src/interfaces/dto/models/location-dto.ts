/**
 * DTO for Location (both live and permanent) entities
 */

/* IMPORTS ================================================================= */

import { LiveLocation } from "@interfaces/entities/po105_core/LiveLocation";
import { PermanentLocation } from "@interfaces/entities/po105_management/PermanentLocation";

/* INTERFACES ============================================================== */

/**
 * Base location information with geographic coordinates and GPS metadata
 */
type BaseLocation = Pick<
  LiveLocation,
  "geog" | "gps_accuracy" | "reading_timestamp"
>;

/**
 * Live location data with real-time GPS coordinates and accuracy information
 */
export interface LiveLocationDto extends BaseLocation {}

/**
 * Base permanent location information with geographic and address details
 */
type BasePermanentLocation = Pick<
  PermanentLocation,
  | "geog"
  | "w3w"
  | "address_line_1"
  | "address_line_2"
  | "city"
  | "country"
  | "postcode"
>;

/**
 * Permanent location data with full address information and What3Words coordinates
 */
export interface PermanentLocationDto extends BasePermanentLocation {}
