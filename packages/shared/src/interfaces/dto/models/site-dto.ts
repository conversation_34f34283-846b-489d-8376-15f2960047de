/**
 * DTO for a Customer's Sites
 */

/* IMPORTS ================================================================= */

import Site from "@interfaces/entities/po105_management/Site";
import { DocumentDto } from "./file-dto";
import { SubstationDto } from "./substation-dto";

/* INTERFACES ============================================================== */
/**
 * Base site information including ID, name and reference
 */
type BaseSite = Pick<Site, "id" | "name" | "reference">;

/**
 * Site data transfer object with associated documents and substations
 */
export interface SiteDto extends BaseSite {
  /** Critical Plant List document */
  cpl_file?: DocumentDto;
  /** Single Line Diagram document */
  sld_file?: DocumentDto;
  /** List of substations associated with this site */
  substations: SubstationDto[];
}
