/**
 * All files' DTOs
 *
 * We `Pick` (filter) fields from the base entity that mirrors the database
 * and aggregate them such that:
 *
 * The parent FileDto contains all shared fields for all files
 *
 * Media types (Image, Audio, Video) extend the FileDto and MediaDto
 *
 */

/* IMPORTS ================================================================= */

import { File } from "@interfaces/entities/po105_storage/File";
import Media from "@interfaces/entities/po105_storage/Media";
import { Image } from "@interfaces/entities/po105_storage/Image";
import { Audio } from "@interfaces/entities/po105_storage/Audio";
import { Video } from "@interfaces/entities/po105_storage/Video";
import { Document } from "@interfaces/entities/po105_storage/Document";

/* INTERFACES ============================================================== */

/**
 * Base file information including description, metadata and tags
 */
type BaseFile = Pick<File, "description" | "metadata" | "tags">;

/**
 * File data transfer object with signed URL for secure access
 */
export interface FileDto extends BaseFile {
  /** Generated signed URL for secure file access */
  signedUrl: string | null;
}

/**
 * Base media information including source and dimensions
 */
type BaseMedia = Pick<Media, "source" | "width" | "height">;

/**
 * Base image information including MIME type
 */
type BaseImage = Pick<Image, "mime_type">;

/**
 * Image file DTO with media properties and MIME type information
 */
export interface ImageDto extends FileDto, BaseMedia, BaseImage {}

/**
 * Base audio information including MIME type
 */
type BaseAudio = Pick<Audio, "mime_type">;

/**
 * Audio file DTO with media properties and MIME type information
 */
export interface AudioDto extends FileDto, BaseMedia, BaseAudio {}

/**
 * Base video information including MIME type
 */
type BaseVideo = Pick<Video, "mime_type">;

/**
 * Video file DTO with media properties and MIME type information
 */
export interface VideoDto extends FileDto, BaseMedia, BaseVideo {}

/**
 * Base document information including MIME type
 */
type BaseDocument = Pick<Document, "mime_type">;

/**
 * Document file DTO with MIME type information
 */
export interface DocumentDto extends FileDto, BaseDocument {}
