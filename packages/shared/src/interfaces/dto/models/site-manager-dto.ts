/**
 * DTO for Site Managers
 */

/* IMPORTS ================================================================= */

import { UserProfileDto } from "./user-dto";
import { CompanyProfileDto } from "./comapny-dto";
import { SiteDto } from "./site-dto";

/* INTERFACES ============================================================== */

/**
 * Site Manager profile with associated company and managed sites
 */
export interface SiteManagerProfileDto extends UserProfileDto {
  /** Company that the site manager works for */
  company: CompanyProfileDto;
  /** List of sites managed by this site manager */
  sites: SiteDto[];
}
