/**
 * DTO for Company entities
 */

/* IMPORTS ================================================================= */

import { ImageDto } from "./file-dto";
import Company from "@interfaces/entities/po105_management/Company";
import { PermanentLocationDto } from "./location-dto";
import { UserProfileDto } from "./user-dto";

/* INTERFACES ============================================================== */

/**
 * Base company information including ID, type, name and contact details
 */
type BaseCompany = Pick<
  Company,
  "id" | "company_type" | "name" | "contact_email"
>;

/**
 * Company profile with additional metadata including images, location and account manager
 */
export interface CompanyProfileDto extends BaseCompany {
  /** Company profile image */
  profile_image?: ImageDto;
  /** Company's permanent location */
  perm_location?: PermanentLocationDto;
  /** Assigned account manager for this company */
  account_manager?: UserProfileDto;
}
