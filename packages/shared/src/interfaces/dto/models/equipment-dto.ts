/**
 * DTO for HV equipment
 */

/* IMPORTS ================================================================= */

import Equipment from "@interfaces/entities/po105_management/Equipment";
import { DocumentDto, ImageDto } from "./file-dto";
import InstalledEquipment from "@interfaces/entities/po105_management/InstalledEquipment";

/* INTERFACES ============================================================== */

// Filtering the Equipment object to only include relevant fields for the DTO
type BaseEquipment = Pick<
  Equipment,
  | "id"
  | "category"
  | "category_other"
  | "reference"
  | "manufacturer"
  | "eq_type"
  | "manufacturing_date"
  | "system_voltage"
  | "current_rating"
  | "spec_file_id"
  | "additional_attributes"
>;

export interface EquipmentDto extends BaseEquipment {
  spec_file?: DocumentDto;
}

// Installed Equipment
export interface InstalledEquipmentImage extends ImageDto {
  description: string;
  capture_timestamp: Date;
}

type BaseInstalledEquipment = Pick<
  InstalledEquipment,
  "placement" | "serial_number" | "commissioning_date" | "last_inspection_date"
>;

export interface InstalledEquipmentDto
  extends EquipmentDto,
    BaseInstalledEquipment {
  images: InstalledEquipmentImage[];
}

//
