/**
 * Base DTO for user profiles (SAPs and Site Managers)
 */

/* IMPORTS ================================================================= */

import { User } from "@interfaces/entities/po105_core/User";
import { ImageDto } from "./file-dto";

/* INTERFACES ============================================================== */

/**
 * Base user profile information including core identity and rating data
 */
type BaseUserProfile = Pick<
  User,
  | "id"
  | "email"
  | "phone_number"
  | "first_name"
  | "last_name"
  | "average_rating"
  | "total_ratings"
  | "user_type"
>;

/**
 * User profile data transfer object used for displaying user information across the application
 */
export interface UserProfileDto extends BaseUserProfile {
  /** User's profile image */
  profile_image?: ImageDto;
}
