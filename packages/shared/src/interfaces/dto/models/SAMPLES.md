# DTO Models' Sample Shapes

This document provides sample shapes for the Data Transfer Objects (DTOs) defined in `packages/shared/src/interfaces/dto/models`.

## Common Base Types (Assumed for `Pick`ed properties)

- **ID Fields** (e.g., `id`, `user_id`): `string` (typically UUID)
- **Text Fields** (e.g., `name`, `email`, `description`): `string`
- **Numeric Fields** (e.g., `average_rating`, `width`): `number`
- **Date/Time Fields** (e.g., `upload_timestamp`): `string` (ISO 8601 format, e.g., "2023-10-27T10:00:00.000Z")
- **Geographic Point (`geog`)**: `object` (e.g., `{ "type": "Point", "coordinates": [longitude, latitude] }`)
- **Metadata/Tags**: `object` or `string[]` respectively.
- **Enum Types** (e.g., `user_type`): `string` (representing enum values)

---

## File DTOs

### `ImageDto`
DTO for image files.

```json
{
  "description": "A sample image",
  "metadata": {
    "alt": "sample"
  },
  "tags": ["nature", "landscape"],
  "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/image.jpg?AWSAccessKeyId=...",
  "source": "CAMERA",   // or "IMAGE_PICKER"
  "width": 6016,
  "height": 4016,
  "mime_type": "image/jpeg"
}
```

### `DocumentDto`
DTO for document files.

```json
{
  "description": "A sample document",
  "metadata": {
    "author": "John Doe",
    "version": "1.2"
  },
  "tags": ["report", "quarterly"],
  "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/document.pdf?AWSAccessKeyId=...",
  "mime_type": "application/pdf"
}
```

---

## Location DTOs

### `PermanentLocationDto`
DTO for permanent location data.

```json
{
  "geog": {
    "type": "Point",
    "coordinates": [-0.127758, 51.507351]
  },
  "w3w": "filled.count.soap",
  "address_line_1": "123 Main Street",
  "address_line_2": "Suite 4B",
  "city": "London",
  "country": "United Kingdom",
  "postcode": "SW1A 1AA"
}
```

---

## Core Entity DTOs

### `UserProfileDto`
Base DTO for user profiles (SAPs and Site Managers).

```json
{
  "id": "user-uuid-12345",
  "email": "<EMAIL>",
  "phone_number": "+************",
  "first_name": "John",
  "last_name": "Doe",
  "average_rating": 4.5,
  "total_ratings": 150,
  "user_type": "SAP", // or "SITE_MANAGER"
  "profile_image": {
    // ImageDto structure
    "description": "User's profile picture",
    "metadata": {},
    "tags": ["optional"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/profile.jpg?AWSAccessKeyId=...",
    "source": "IMAGE_PICKER",
    "width": 500,
    "height": 500,
    "mime_type": "image/png"
  }
}
```

### `EquipmentDto`
DTO for HV equipment.

```json
{
  "id": "equip-uuid-67890",
  "category": "SWITCHGEAR",
  "category_other": null, // string if category is "Other"
  "reference": "SWG-001",
  "manufacturer": "ABB",
  "eq_type": "GIS",
  "manufacturing_date": "2020-01-15T00:00:00.000Z",
  "system_voltage": 132000, // Volts
  "current_rating": 1250, // Amps
  "spec_file_id": "doc-uuid-specfile",
  "additional_attributes": {
    "insulation_medium": "SF6",
    "short_circuit_rating_kA": 40
  },
  "spec_file": { // Optional DocumentDto (see above)
    "description": "Equipment Specification Sheet",
    "metadata": {},
    "tags": ["specification", "switchgear"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/spec.pdf?AWSAccessKeyId=...",
    "mime_type": "application/pdf"
  }
}
```

### `SubstationDto`
DTO for a Substation in a Customer's Site.

```json
{
  "id": "substation-uuid-123",
  "reference": "SUB-A1",
  "name": "Main Substation Alpha",
  "number": "1",
  "perm_location": {
    // PermanentLocationDto structure (see above)
    "geog": {
      "type": "Point",
      "coordinates": [-0.128800, 51.508500]
    },
    "w3w": "index.home.raft",
    "address_line_1": "1 Power Grid Avenue",
    "address_line_2": null,
    "city": "London",
    "country": "United Kingdom",
    "postcode": "SW1A 2AA"
  },
  "equipment": [
    {
      // EquipmentDto structure (see above)
      "id": "equip-uuid-transformer-01",
      "category": "Transformer",
      "category_other": null,
      "reference": "TR-001",
      "manufacturer": "Siemens",
      "eq_type": "Power Transformer",
      "manufacturing_date": "2019-05-20T00:00:00.000Z",
      "system_voltage": 132000,
      "current_rating": 800, // Primary side example
      "spec_file_id": "doc-uuid-trafo-spec",
      "additional_attributes": {
        "cooling_type": "ONAN/ONAF",
        "vector_group": "Dyn11"
      },
      "spec_file": {
        "description": "Transformer T1 Specification",
        "metadata": {},
        "tags": ["transformer", "specification"],
        "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/trafo_spec.pdf?AWSAccessKeyId=...",
        "mime_type": "application/pdf"
      }
    }
  ]
}
```

### `SiteDto`
DTO for a Customer's Site.

```json
{
  "id": "site-uuid-456",
  "name": "North London Data Center",
  "reference": "NLDC-001",
  "perm_location": {
    // PermanentLocationDto structure (see above)
    "geog": {
      "type": "Point",
      "coordinates": [-0.129900, 51.509500]
    },
    "w3w": "calm.splints.rally",
    "address_line_1": "2 Data Drive",
    "address_line_2": "Tech Park",
    "city": "London",
    "country": "United Kingdom",
    "postcode": "N1 1AA"
  },
  "cpl_file": { // Optional DocumentDto
    "description": "Customer Premises Layout Plan",
    "metadata": {},
    "tags": ["CPL", "layout"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/cpl.pdf?AWSAccessKeyId=...",
    "mime_type": "application/pdf"
  },
  "sld_file": { // Optional DocumentDto
    "description": "Single Line Diagram",
    "metadata": {},
    "tags": ["SLD", "electrical"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sld.pdf?AWSAccessKeyId=...",
    "mime_type": "application/pdf"
  },
  "substations": [
    {
      // SubstationDto structure (see above)
      "id": "substation-uuid-123",
      "reference": "SUB-A1",
      "name": "Main Substation Alpha",
      "number": "1",
      "perm_location": {
        "geog": { "type": "Point", "coordinates": [-0.128800, 51.508500] },
        "w3w": "index.home.raft",
        "address_line_1": "1 Power Grid Avenue",
        "address_line_2": null,
        "city": "London",
        "country": "United Kingdom",
        "postcode": "SW1A 2AA"
      },
      "equipment": [
        {
          "id": "equip-uuid-transformer-01",
          "category": "Transformer",
          "category_other": null,
          "reference": "TR-001",
          "manufacturer": "Siemens",
          "eq_type": "Power Transformer",
          "manufacturing_date": "2019-05-20T00:00:00.000Z",
          "system_voltage": 132000,
          "current_rating": 800,
          "spec_file_id": "doc-uuid-trafo-spec",
          "additional_attributes": { "insulation_type": "ONAN/ONAF", "vector_group": "Dyn11" },
          "spec_file": {
            "description": "Transformer T1 Specification",
            "metadata": {},
            "tags": ["transformer", "specification"],
            "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/trafo_spec.pdf?AWSAccessKeyId=...",
            "mime_type": "application/pdf"
          }
        }
      ]
    }
  ]
}
```

### `CompanyProfileDto`
DTO for Company entities.

```json
{
  "id": "company-uuid-3219",
  "company_type": "CUSTOMER", // or "PARTNER" | "SUPPLIER"
  "name": "Global Energy Corp",
  "contact_email": "<EMAIL>",
  "profile_image": {
    // ImageDto structure
    "description": "Company Logo",
    "metadata": {},
    "tags": ["logo", "brand"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/company_logo.png?AWSAccessKeyId=...",
    "source": "IMAGE_PICKER",
    "width": 300,
    "height": 150,
    "mime_type": "image/png"
  },
  "perm_location": { // Optional PermanentLocationDto
    "geog": { "type": "Point", "coordinates": [-0.130000, 51.510000] },
    "w3w": "kind.lamps.shows",
    "address_line_1": "1 Energy Plaza",
    "address_line_2": "HQ Building",
    "city": "London",
    "country": "United Kingdom",
    "postcode": "EC1A 1BB"
  },
  "account_manager": { // Optional UserProfileDto
    "id": "user-uuid-am-789",
    "email": "<EMAIL>",
    "phone_number": "+************",
    "first_name": "Alice",
    "last_name": "Smith",
    "average_rating": 4.8,
    "total_ratings": 50,
    "user_type": "AccountManager", // Example, or could be a SAP/SiteManager
    "profile_image": null
  }
}
```

---

## User Role Specific DTOs

### `SapDto`
DTO for SAPs (Service Application Partners), as seen by themselves.

```json
{
  // Inherits UserProfileDto fields
  "id": "sap-uuid-12345",
  "email": "<EMAIL>",
  "phone_number": "+442*********",
  "first_name": "Bob",
  "last_name": "The Builder",
  "average_rating": 4.9,
  "total_ratings": 210,
  "user_type": "SAP",
  "profile_image": {
    "description": "SAP's profile picture",
    "metadata": {},
    "tags": ["profile", "sap"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sap_profile.jpg?AWSAccessKeyId=...",
    "source": "CAMERA",
    "width": 600,
    "height": 600,
    "mime_type": "image/jpeg"
  },
  // SapDto specific fields
  "partner": { // Optional
    "company": {
      // CompanyProfileDto structure (see above, simplified)
      "id": "company-uuid-325",
      "company_type": "Partner",
      "name": "Reliable HV Services Ltd.",
      "contact_email": "<EMAIL>",
      // ... other CompanyProfileDto fields
      "profile_image": { /* ... */ },
      "perm_location": { /* ... */ }
    }
  }
}
```

### `SiteManagerProfileDto`
DTO for Site Managers.

```json
{
  // Extends UserProfileDto fields
  "id": "sitemgr-uuid-67890",
  "email": "<EMAIL>",
  "phone_number": "+************",
  "first_name": "Carol",
  "last_name": "Danvers",
  "average_rating": 4.2,
  "total_ratings": 75,
  "user_type": "SiteManager",
  "profile_image": {
    "description": "Site Manager's profile picture",
    "metadata": {},
    "tags": ["profile", "sitemanager"],
    "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sitemgr_profile.jpg?AWSAccessKeyId=...",
    "source": "IMAGE_PICKER",
    "width": 400,
    "height": 400,
    "mime_type": "image/png"
  },
  // SiteManagerProfileDto specific fields
  "customer_id": "cust-abc-123", // From BaseSiteManager Pick
  "company": {
    // CompanyProfileDto structure (see above, simplified)
    "id": "company-uuid-635",
    "company_type": "Client",
    "name": "MegaCorp Industries",
    "contact_email": "<EMAIL>",
    // ... other CompanyProfileDto fields
    "profile_image": { /* ... */ },
    "perm_location": { /* ... */ }
  },
  "sites": [
    {
      // SiteDto structure (see above for full spec, simplified here for conciseness)
      "id": "site-uuid-456",
      "name": "North London Data Center",
      "reference": "NLDC-001",
      // ... other SiteDto fields
      "perm_location": { /* ... */ },
      "substations": [ /* ... */ ]
    }
  ]
}
``` 