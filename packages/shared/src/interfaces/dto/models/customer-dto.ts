/**
 * DTO for Customers
 *
 * An aggregation of Company and Sites
 *
 */

/* IMPORTS ================================================================= */

import { Customer } from "@interfaces/entities/po105_management/Customer";
import { SiteDto } from "./site-dto";
import { CompanyProfileDto } from "./comapny-dto";

/* INTERFACES ============================================================== */

/**
 * Base customer information including unique identifier
 */
type BaseCustomer = Pick<Customer, "id">;

/**
 * Customer data transfer object aggregating company and site information
 */
export interface CustomerDto extends BaseCustomer {
  /** Company profile information for this customer */
  company: CompanyProfileDto;
  /** List of sites owned by this customer */
  sites: SiteDto[];
}
