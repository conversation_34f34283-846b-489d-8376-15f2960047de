# LoginResponse Sample Shapes

This document provides sample shapes for the `LoginResponse` type, which is a discriminated union representing the response from a successful user login.

---

## `SapLoginResponse`

This is the response shape when a Service Application Partner (SAP) logs in.

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
  "profile": {
    // SapDto structure
    "id": "sap-uuid-12345",
    "email": "<EMAIL>",
    "phone_number": "+************",
    "first_name": "<PERSON>",
    "last_name": "The Builder",
    "average_rating": 4.9,
    "total_ratings": 210,
    "user_type": "SAP",
    "profile_image": {
      "description": "SAP's profile picture",
      "metadata": {},
      "tags": ["profile", "sap"],
      "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sap_profile.jpg?AWSAccessKeyId=...",
      "source": "CAMERA",
      "width": 600,
      "height": 600,
      "mime_type": "image/jpeg"
    },
    "qualification": "HV Switching Operations",
    "availability": true,
    "total_callouts": 45,
    "partner": {
      "company": {
        "id": "company-uuid-325",
        "company_type": "PARTNER",
        "name": "Reliable HV Services Ltd.",
        "contact_email": "<EMAIL>",
        "profile_image": {
          "description": "Company Logo",
          "metadata": {},
          "tags": ["logo", "brand"],
          "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/partner_logo.png?AWSAccessKeyId=...",
          "source": "IMAGE_PICKER",
          "width": 300,
          "height": 150,
          "mime_type": "image/png"
        },
        "perm_location": {
          "geog": { "type": "Point", "coordinates": [-0.14, 51.52] },
          "w3w": "partner.office.location",
          "address_line_1": "1 Partner Road",
          "address_line_2": "Suite P",
          "city": "London",
          "country": "United Kingdom",
          "postcode": "WC1 1NE"
        },
        "account_manager": null
      },
      "sla_file": {
        "description": "Partner SLA Document",
        "metadata": {},
        "tags": ["sla", "legal"],
        "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/partner_sla.pdf?AWSAccessKeyId=...",
        "mime_type": "application/pdf"
      }
    },
    "sla_file": {
      "description": "Individual SAP SLA",
      "metadata": {},
      "tags": ["sla", "individual"],
      "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sap_sla.pdf?AWSAccessKeyId=...",
      "mime_type": "application/pdf"
    },
    "permanent_location": {
      "geog": { "type": "Point", "coordinates": [-0.125, 51.515] },
      "w3w": "sap.home.location",
      "address_line_1": "123 SAP Street",
      "address_line_2": "Apt 4B",
      "city": "London",
      "country": "United Kingdom",
      "postcode": "SW1 1AA"
    }
  }
}
```

---

## `SiteManagerLoginResponse`

This is the response shape when a Site Manager logs in.

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************._x1gVAPFw8x0_qYjTzC6XqC_0gY9cVpY2lLsVvG8wBc",
  "profile": {
    // SiteManagerProfileDto structure
    "id": "sitemgr-uuid-67890",
    "email": "<EMAIL>",
    "phone_number": "+************",
    "first_name": "Carol",
    "last_name": "Danvers",
    "average_rating": 4.2,
    "total_ratings": 75,
    "user_type": "SITE_MANAGER",
    "profile_image": {
      // ImageDto
      "description": "Site Manager's profile picture",
      "metadata": {},
      "tags": ["profile", "sitemanager"],
      "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sitemgr_profile.jpg?AWSAccessKeyId=...",
      "source": "CAMERA",
      "width": 400,
      "height": 400,
      "mime_type": "image/png"
    },
    "company": {
      "id": "company-uuid-635",
      "company_type": "CLIENT",
      "name": "MegaCorp Industries",
      "contact_email": "<EMAIL>",
      "profile_image": {
        "description": "MegaCorp Logo",
        "metadata": {},
        "tags": ["logo", "client"],
        "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/megacorp_logo.png?AWSAccessKeyId=...",
        "source": "IMAGE_PICKER",
        "width": 400,
        "height": 100,
        "mime_type": "image/png"
      },
      "perm_location": {
        "geog": { "type": "Point", "coordinates": [-0.13, 51.51] },
        "w3w": "kind.lamps.shows",
        "address_line_1": "1 Energy Plaza",
        "address_line_2": "HQ Building",
        "city": "London",
        "country": "United Kingdom",
        "postcode": "EC1A 1BB"
      },
      "account_manager": {
        "id": "user-uuid-am-789",
        "email": "<EMAIL>",
        "phone_number": "+************",
        "first_name": "Alice",
        "last_name": "Smith",
        "average_rating": 4.8,
        "total_ratings": 50,
        "user_type": "CUSTOMER_MANAGER",
        "profile_image": {
          "description": "Account Manager's profile picture",
          "metadata": {},
          "tags": ["profile"],
          "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/am_profile.jpg?AWSAccessKeyId=...",
          "source": "IMAGE_PICKER",
          "width": 500,
          "height": 500,
          "mime_type": "image/jpeg"
        }
      }
    },
    "sites": [
      {
        "id": "site-uuid-456",
        "name": "North London Data Center",
        "reference": "NLDC-001",
        "cpl_file": {
          "description": "Customer Premises Layout Plan",
          "metadata": {},
          "tags": ["CPL", "layout"],
          "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/cpl.pdf?AWSAccessKeyId=...",
          "mime_type": "application/pdf"
        },
        "sld_file": {
          "description": "Single Line Diagram",
          "metadata": {},
          "tags": ["SLD", "electrical"],
          "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/sld.pdf?AWSAccessKeyId=...",
          "mime_type": "application/pdf"
        },
        "substations": [
          {
            "id": "substation-uuid-123",
            "reference": "SUB-A1",
            "name": "Main Substation Alpha",
            "number": "1",
            "perm_location": {
              "geog": { "type": "Point", "coordinates": [-0.1288, 51.5085] },
              "w3w": "index.home.raft",
              "address_line_1": "1 Power Grid Avenue",
              "address_line_2": null,
              "city": "London",
              "country": "United Kingdom",
              "postcode": "SW1A 2AA"
            },
            "equipment": [
              {
                "id": "equip-uuid-transformer-01",
                "category": "Transformer",
                "category_other": null,
                "reference": "TR-001",
                "manufacturer": "Siemens",
                "eq_type": "Power Transformer",
                "manufacturing_date": "2019-05-20T00:00:00.000Z",
                "system_voltage": 132000,
                "current_rating": 800,
                "spec_file_id": "doc-uuid-trafo-spec",
                "additional_attributes": { "insulation_type": "ONAN/ONAF" },
                "spec_file": {
                  "description": "Transformer T1 Specification",
                  "metadata": {},
                  "tags": ["transformer", "specification"],
                  "signedUrl": "https://example-bucket.s3.amazonaws.com/path/to/trafo_spec.pdf?AWSAccessKeyId=...",
                  "mime_type": "application/pdf"
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```
