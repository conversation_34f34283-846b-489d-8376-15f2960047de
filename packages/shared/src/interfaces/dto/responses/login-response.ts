/**
 * API Response Interface for User Login.
 * Uses a discriminated union based on the 'role' field.
 **/

/* IMPORTS ================================================================ */

import { SapDto } from "../models/sap-dto";
import { SiteManagerProfileDto } from "../models/site-manager-dto";

/**
 * Base structure for successful login response
 */
interface BaseLoginResponse {
  /** JWT authentication token */
  token: string;
}

/**
 * Login response for SAP (Senior Authorised Person) users
 */
interface SapLoginResponse extends BaseLoginResponse {
  /** SAP user profile information */
  profile: SapDto;
}

/**
 * Login response for Site Manager users
 */
interface SiteManagerLoginResponse extends BaseLoginResponse {
  /** Site Manager user profile information */
  profile: SiteManagerProfileDto;
}

/**
 * Discriminated union for login response payload based on user role
 */
export type LoginResponse = SapLoginResponse | SiteManagerLoginResponse;
