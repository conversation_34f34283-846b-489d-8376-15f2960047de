/**
 * Standard structure for API error responses.
 * Based on BaseError.resJson getter.
 */

/**
 * Standard structure for API error responses based on BaseError.resJson getter
 */
export interface ApiErrorResponse {
  /** Error reference identifier */
  ref: string;
  /** Error title */
  title: string;
  /** Human-readable error message */
  message: string;
  /** HTTP status code */
  status: number;
  /** Formatted error message if available */
  formattedMessage?: string;
}
