/**
 * API Response Interface for Emergency Declaration
 */

/**
 * Information about a nearby SAP available for emergency response
 */
interface NearbySap {
  /** SAP unique identifier */
  id: string;
  /** SAP first name */
  first_name: string;
  /** SAP last name */
  last_name: string;
  /** URL to SAP's profile picture */
  profile_picture_url?: string;
  /** SAP rating (0-5) */
  rating: number;
  /** Distance from emergency site in miles */
  distance_miles: number;
  /** Estimated arrival time in minutes */
  estimated_arrival_mins: number;
  /** SAP's current location coordinates */
  location: {
    /** Latitude coordinate */
    latitude: number;
    /** Longitude coordinate */
    longitude: number;
  };
}

/**
 * Response interface for emergency declaration containing work order details and nearby SAPs
 */
export interface EmergencyDeclarationResponse {
  /** Generated work order ID for the emergency */
  work_order_id: string;
  /** Generated callout request ID */
  callout_request_id: string;
  /** Timestamp when the emergency was declared */
  declaration_timestamp: string;
  /** Current status of the emergency callout */
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  /** Estimated response time in minutes */
  estimated_response_time?: number;
  /** List of nearby SAPs available for response */
  nearby_saps: NearbySap[];
}
