/**
 * API Request Interface for Emergency Declaration
 */

// emergency-declaration-request.ts
/**
 * Request interface for declaring an emergency at a site
 */
export interface EmergencyDeclarationRequest {
  /** ID of the site where emergency occurred */
  site_id: string;
  /** Detailed description of the emergency */
  description: string;
  /** Type of fault that occurred */
  fault_type: string;
  /** Severity level of the emergency (1-5, where 5 is most severe) */
  severity: number;
  /** Priority level of the emergency (1-5, where 5 is highest priority) */
  priority: number;
}
