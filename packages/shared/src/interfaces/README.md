# Interfaces Structure

This directory contains all TypeScript interfaces used across the application, organized into two main categories:

## 1. Entities (`/entities`)
- Auto-generated by <PERSON><PERSON>
- Represents the denormalized database schema
- Direct mapping to database tables
- Used primarily in the backend for database operations
- Should not be exposed directly to the frontend

## 2. DTO (Data Transfer Objects) (`/dto`)
Contains three subdirectories:

### 2.1 Requests (`/requests`)
- Interfaces for incoming API requests
- Defines the expected structure of data sent from frontend to backend
- Includes validation and type safety for API endpoints

### 2.2 Responses (`/responses`)
- Interfaces for API responses
- Contains structured data returned from backend to frontend
- Includes composite types like `LoginResponse` that combine multiple data types

### 2.3 Models (`/models`)
- Functional, denormalized representations of entities
- Combines related data from multiple tables
- Ready to be embedded in requests or responses
- Examples:
  - `SAPModel`: Combines SAP data with profile image and partner information
  - `SiteManagerModel`: Combines site manager data with company and location details

## Usage Guidelines
1. Always use DTO interfaces for frontend-backend communication
2. Use entity interfaces only within backend services and queries
3. When creating new endpoints, define corresponding request/response interfaces in the DTO directory
4. For complex data structures, create models in the DTO/models directory

