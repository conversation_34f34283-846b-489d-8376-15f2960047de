/* IMPORTS ================================================================= */

import AuthService from '../../services/AuthService';
import { supabase } from '../../database/connection';
import { SapQueries, SiteManagerQueries } from '../../database/queries';
import UserType from '@entities/po105_core/UserTypeEnum';
import { AuthError } from '../../models/errors';

/**
 * Mock Supabase client
 */
jest.mock('../../database/connection', () => ({
    supabase: {
        auth: {
            signInWithPassword: jest.fn()
        }
    }
}));


/**
 * Mock database queries
 */
jest.mock('../../database/queries', () => ({
    SapQueries: {
        getFullProfile: jest.fn()
    },
    SiteManagerQueries: {
        getFullProfile: jest.fn()
    }
}));

/**
 * Test suite for the AuthService
 */
describe('AuthService', () => {
    // Clear mocks before each test
    beforeEach(() => {
        jest.clearAllMocks();
    });

    // Test suite for the login method
    describe('login', () => {
        // Mock login request
        const mockLoginRequest = {
            email: '<EMAIL>',
            password: 'password123'
        };

        // Mock Supabase session
        const mockSession = {
            access_token: 'mock-access-token'
        };

        // Mock SAP user profiles
        const mockSapProfile = {
            id: 'sap-123',
            name: 'Test SAP',
            email: '<EMAIL>',
            phone: '1234567890'
        };

        // Mock Site Manager user profiles
        const mockSiteManagerProfile = {
            id: 'sm-123',
            name: 'Test Site Manager',
            email: '<EMAIL>',
            phone: '1234567890',
            company: {
                id: 'company-123',
                name: 'Test Company'
            },
            sites: []
        };

        //=========================================================
        // Test for SAP login
        //=========================================================
        it('should successfully login a SAP user', async () => {
            // Mock Supabase auth response
            (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
                data: {
                    user: {
                        id: 'user-123',
                        user_metadata: {
                            user_type: UserType.SAP
                        }
                    },
                    session: mockSession
                },
                error: null
            });

            // Mock SAP profile query
            (SapQueries.getFullProfile as jest.Mock).mockResolvedValue(mockSapProfile);

            const result = await AuthService.login(mockLoginRequest);

            expect(result).toEqual({
                token: mockSession.access_token,
                profile: mockSapProfile
            });
            expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith(mockLoginRequest);
            expect(SapQueries.getFullProfile).toHaveBeenCalledWith('user-123');
        });

        // ==========================================================
        // Test for Site Manager login
        // ==========================================================
        it('should successfully login a Site Manager user', async () => {
            // Mock Supabase auth response
            (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
                data: {
                    user: {
                        id: 'user-123',
                        user_metadata: {
                            user_type: UserType.SITE_MANAGER
                        }
                    },
                    session: mockSession
                },
                error: null
            });

            // Mock Site Manager profile query
            (SiteManagerQueries.getFullProfile as jest.Mock).mockResolvedValue(mockSiteManagerProfile);

            const result = await AuthService.login(mockLoginRequest);

            expect(result).toEqual({
                token: mockSession.access_token,
                profile: mockSiteManagerProfile
            });
            expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith(mockLoginRequest);
            expect(SiteManagerQueries.getFullProfile).toHaveBeenCalledWith('user-123');
        });

        //==========================================================
        // Test for Supabase authentication failure
        //==========================================================
        it('should throw AuthError when Supabase authentication fails', async () => {
            // Mock Supabase auth error
            (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
                data: { user: null, session: null },
                error: { message: 'Invalid credentials' }
            });

            await expect(AuthService.login(mockLoginRequest)).rejects.toThrow(AuthError);
            expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith(mockLoginRequest);
        });

        //==========================================================
        // Test for user profile not found
        //==========================================================
        it('should throw AuthError when user profile is not found', async () => {
            // Mock successful Supabase auth
            (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
                data: {
                    user: {
                        id: 'user-123',
                        user_metadata: {
                            user_type: UserType.SAP
                        }
                    },
                    session: mockSession
                },
                error: null
            });

            // Mock profile not found
            (SapQueries.getFullProfile as jest.Mock).mockResolvedValue(null);

            await expect(AuthService.login(mockLoginRequest)).rejects.toThrow(AuthError);
            expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith(mockLoginRequest);
            expect(SapQueries.getFullProfile).toHaveBeenCalledWith('user-123');
        });


        //==========================================================
        // Test for unsupported user type
        //==========================================================
        it('should throw AuthError for unsupported user type', async () => {
            // Mock Supabase auth with unsupported user type
            (supabase.auth.signInWithPassword as jest.Mock).mockResolvedValue({
                data: {
                    user: {
                        id: 'user-123',
                        user_metadata: {
                            user_type: 'UNSUPPORTED_TYPE'
                        }
                    },
                    session: mockSession
                },
                error: null
            });

             // Mock profile queries to ensure they're not called
            (SapQueries.getFullProfile as jest.Mock).mockResolvedValue(null);
            (SiteManagerQueries.getFullProfile as jest.Mock).mockResolvedValue(null);

            // Verify Supabase was called
            await expect(AuthService.login(mockLoginRequest)).rejects.toThrow(AuthError);
            expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith(mockLoginRequest);

            // Verify profile queries were not called
            expect(SapQueries.getFullProfile).not.toHaveBeenCalled();
            expect(SiteManagerQueries.getFullProfile).not.toHaveBeenCalled();
        });
    });
});