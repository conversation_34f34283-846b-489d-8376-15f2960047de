/**
 * Site-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import type { CustomerId } from '@shared/interfaces/entities/po105_management/Customer';
import type { SiteId } from '@shared/interfaces/entities/po105_management/Site';
import type { SiteDto } from '@shared/interfaces/dto/models/site-dto';
import SubstationQueries from './SubstationQueries';
import { sql } from 'kysely';

/* QUERIES ================================================================ */

class SiteQueries {
    /**
     * Fetches all SiteDto for a given customer ID.
     */
    public static async getSiteDtosByCustomerId(
        customerId: CustomerId, 
        includeSubstations: boolean = true,
        includeEquipment: boolean = true
    ): Promise<SiteDto[]> {
        const rawSites = await db
            .selectFrom('po105_management.site as s')

            .leftJoin('po105_storage.file as cplf', (join) =>
                join.on((eb) => eb(eb.ref('cplf.id'), '=', eb.ref('s.cpl_spreadsheet_id')))
            )
            .leftJoin('po105_storage.document as cpld', 'cpld.file_id', 'cplf.id')
            // SLD File Joins - Temporarily commented out to fix other errrors
            .where('s.customer_id', '=', customerId)
            .where('s.deleted_at', 'is', null)
            .select(
                [
                    's.id as id',
                    's.name as name',
                    's.reference',
                    's.cpl_spreadsheet_id as cpl_file_id_fk',
                ]
            )
            .execute();

        const resultDtos: SiteDto[] = await Promise.all(rawSites.map(async (rs) => {
            const substationDtos = rs.id
                ? await SubstationQueries.getSubstationDtosBySiteId(
                    rs.id as SiteId,
                    includeEquipment
                )
                : [];

            return {
                id: rs.id as SiteId,
                name: rs.name,
                reference: rs.reference,
                substations: substationDtos,
                // cpl_file and sld_file are DocumentDto | undefined in SiteDto. Placeholder for now.
                cpl_file: undefined, 
                sld_file: undefined
            }  as unknown as SiteDto;
        }));

        return resultDtos;
    }
}

export default SiteQueries; 