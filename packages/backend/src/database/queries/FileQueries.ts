/**
 * File-related database queries
 */

/* IMPORTS ================================================================ */

import { db, supabase } from '../connection';
import type { FileId } from '@shared/interfaces/entities/po105_storage/File';
import type { DocumentDto, ImageDto } from '@shared/interfaces/dto/models/file-dto';
import FileTypeEnum from '@shared/interfaces/entities/po105_storage/FileTypeEnum';
import { logger } from '../../utils/logger';

const DEFAULT_IMAGE_DIMENSION = 100;

/* CLASS =================================================================== */

/**
 * File-related database queries for fetching file metadata and constructing DTOs.
 * URL signing is handled by FileService.
 */
class FileQueries {
    
    public async getFileDtoById(fileId: FileId | null | undefined): Promise<ImageDto | DocumentDto | null> {
        if (!fileId) return null;
        const rawDetails = await db 
            .selectFrom('po105_storage.file as f')
            .leftJoin('po105_storage.media as m', 'm.file_id', 'f.id')
            .leftJoin('po105_storage.image as i', 'i.file_id', 'f.id')
            .leftJoin('po105_storage.document as d', 'd.file_id', 'f.id')
            .where('f.id', '=', fileId)
            .select([
                'f.id',
                'f.description',
                'f.metadata as file_metadata',
                'f.tags',
                'f.file_type',
                'm.source as media_source',
                'm.width as media_width',
                'm.height as media_height',
                'i.mime_type as image_mime_type',
                'd.mime_type as document_mime_type'
            ])
            .executeTakeFirst();

        if (!rawDetails) {
            logger.warn({ fileId }, 'File details not found for DTO creation in FileQueries.');
            return null;
        }

        const fileDtoPart = {
            description: rawDetails.description as string | null,
            metadata: rawDetails.file_metadata, 
            tags: rawDetails.tags as string[] | null,
            signedUrl: null,
        };
        
        if (rawDetails.file_type === FileTypeEnum.IMAGE) {
            const imageDto: ImageDto = {
                ...fileDtoPart,
                source: rawDetails.media_source as any, 
                width: ((rawDetails.media_width as number | null) ?? DEFAULT_IMAGE_DIMENSION) as any, 
                height: ((rawDetails.media_height as number | null) ?? DEFAULT_IMAGE_DIMENSION) as any,
                mime_type: rawDetails.image_mime_type as any,
            };
            return imageDto;
        } else if (rawDetails.file_type === FileTypeEnum.DOCUMENT) { 
            const documentDto: DocumentDto = {
                ...fileDtoPart,
                mime_type: rawDetails.document_mime_type as any, 
            };
            return documentDto;
        } else if (rawDetails.file_type === FileTypeEnum.VIDEO) {
            logger.warn({fileId, fileType: rawDetails.file_type}, 'Video file type detected but not explicitly handled by getFileDtoById. Returning null.');
            return null; 
        } else {
            logger.warn({fileId, fileType: rawDetails.file_type}, 'Unknown or unhandled file type in getFileDtoById. Returning null.');
            return null;
        }
    }

    /**
     * Gets file metadata required for URL signing
     * @param fileId - ID of the file
     * @returns Basic file metadata for URL signing or null if not found
     */
    public async getFileMetadataById(fileId: FileId): Promise<{id: FileId, bucket_name: string, bucket_id: string, file_type: FileTypeEnum} | null> {
        if (!fileId) return null;

        try {
            const fileMetadata = await db
                .selectFrom('po105_storage.file as f')
                .where('f.id', '=', fileId)
                .select([
                    'f.id',
                    'f.bucket_name',
                    'f.bucket_id',
                    'f.file_type'
                ])
                .executeTakeFirst();

            if (!fileMetadata) {
                logger.warn({ fileId }, 'File metadata not found in getFileMetadataById');
                return null;
            }

            return {
                id: fileMetadata.id as FileId,
                bucket_name: fileMetadata.bucket_name as string,
                bucket_id: fileMetadata.bucket_id as string,
                file_type: fileMetadata.file_type as FileTypeEnum
            };
        } catch (error) {
            logger.error({ fileId, error }, 'Error retrieving file metadata');
            return null;
        }
    }

    /**
     * Gets metadata for multiple files for batch URL signing
     * @param fileIds - Array of file IDs
     * @returns Array of file metadata objects
     */
    public async getFilesMetadataByIds(fileIds: FileId[]): Promise<Array<{id: FileId, bucket_name: string, bucket_id: string, file_type: FileTypeEnum}>> {
        if (!fileIds.length) return [];

        try {
            const filesMetadata = await db
                .selectFrom('po105_storage.file as f')
                .where('f.id', 'in', fileIds)
                .select([
                    'f.id',
                    'f.bucket_name',
                    'f.bucket_id',
                    'f.file_type'
                ])
                .execute();

            if (!filesMetadata.length) {
                logger.warn({ fileIds }, 'No files found in getFilesMetadataByIds');
                return [];
            }

            return filesMetadata.map(file => ({
                id: file.id as FileId,
                bucket_name: file.bucket_name as string,
                bucket_id: file.bucket_id as string,
                file_type: file.file_type as FileTypeEnum
            }));
        } catch (error) {
            logger.error({ fileIds, error }, 'Error retrieving multiple files metadata');
            return [];
        }
    }
}

export default new FileQueries(); 