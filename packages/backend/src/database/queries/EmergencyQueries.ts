/**
 * Emergency-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import { sql } from 'kysely';
import type { UserId } from '@shared/interfaces/entities/po105_core/User';
import type { SiteId } from '@shared/interfaces/entities/po105_management/Site';
import type { WorkOrderId } from '@shared/interfaces/entities/po105_job/WorkOrder';
import type { CalloutRequestId } from '@shared/interfaces/entities/po105_job/CalloutRequest';
import type { CalloutId } from '@shared/interfaces/entities/po105_job/Callout';
import type { EmergencyDeclarationResponse } from '@shared/interfaces/dto/responses/EmergencyDeclarationResponse';
import type { PermanentLocationId } from '@shared/interfaces/entities/po105_management/PermanentLocation';
import type { FileId } from '@shared/interfaces/entities/po105_storage/File';
import FileService from '../../services/FileService';
import WorkTypeEnum from '@shared/interfaces/entities/po105_job/WorkTypeEnum';
import FaultTypeEnum from '@shared/interfaces/entities/po105_job/FaultTypeEnum';

/* QUERIES ================================================================ */

class EmergencyQueries {
    /**
     * Get work order details and verify access
     */
    static async getWorkOrderDetails(workOrderId: WorkOrderId, userId: UserId) {
        return await db
            .selectFrom('po105_job.work_order as wo')
            .innerJoin('po105_management.site as s', 'wo.site_id', 's.id')
            .innerJoin('po105_management.site_manager as sm', 'sm.customer_id', 's.customer_id')
            .select([
                'wo.id',
                'wo.site_id',
            ])
            .where('wo.id', '=', workOrderId)
            .where('sm.user_id', '=', userId)
            .where('wo.deleted_at', 'is', null)
            .executeTakeFirst();
    }

    /**
     * Calculate SAP's distance to a site location
     */
    static async calculateSapDistanceAndTime(sapId: UserId, siteLocationId: PermanentLocationId) {
        return await db
            .selectFrom('po105_core.sap as s')
            .innerJoin('po105_core.live_location as l', join => 
                join.on(eb => eb.ref('s.last_known_location_id'), '=', eb => eb.ref('l.id'))
            )
            .innerJoin('po105_management.permanent_location as pl', join =>
                join.on('pl.id', '=', siteLocationId)
            )
            .select(sql<{ distance_miles: number }>`
                json_build_object(
                    'distance_miles',
                    ST_Distance(
                        l.geog::geography,
                        pl.geog::geography
                    ) / 1609.34
                )
            `.as('result'))
            .where('s.user_id', '=', sapId)
            .where('s.deleted_at', 'is', null)
            .where('l.deleted_at', 'is', null)
            .executeTakeFirst()
            .then(result => result?.result);
    }

    /**
     * Find nearby SAPs within a 50-mile radius
     */
    static async findNearbySaps(siteLocation: { latitude: number; longitude: number }) {
        const nearbySapsRaw = await db
            .selectFrom('po105_core.user as u')
            .innerJoin('po105_core.sap as s', 'u.id', 's.user_id')
            .innerJoin('po105_core.live_location as l', 's.last_known_location_id', 'l.id')
            .leftJoin('po105_storage.file as f', 'u.profile_image_id', 'f.id')
            .leftJoin('po105_core.user_feedback as uf', 'uf.recipient_id', 'u.id')
            .select(sql<{
                id: UserId;
                first_name: string;
                last_name: string;
                profile_image_bucket_name: string | null;
                profile_image_owner_id: string | null;
                profile_image_file_name: string | null;
                rating: number;
                distance_miles: number;
                location: { longitude: number; latitude: number };
            }>`
                json_build_object(
                    'id', u.id,
                    'first_name', u.first_name,
                    'last_name', u.last_name,
                    'profile_image_bucket_name', f.bucket_name,
                    'profile_image_owner_id', f.owner_id,
                    'profile_image_file_name', f.file_name,
                    'rating', COALESCE(AVG(uf.rating), 5),
                    'distance_miles', 
                        ST_Distance(
                            l.geog::geography,
                            ST_SetSRID(ST_MakePoint(${siteLocation.longitude}, ${siteLocation.latitude}), 4326)::geography
                        ) / 1609.34,
                    'location', json_build_object(
                        'longitude', ST_X(l.geog::geometry),
                        'latitude', ST_Y(l.geog::geometry)
                    )
                )
            `.as('result'))
            .where('u.deleted_at', 'is', null)
            .where('s.deleted_at', 'is', null)
            .where('l.deleted_at', 'is', null)
            .where(sql<boolean>`
                ST_DWithin(
                    l.geog::geography,
                    ST_SetSRID(ST_MakePoint(${siteLocation.longitude}, ${siteLocation.latitude}), 4326)::geography,
                    80467.2
                )
            `)
            .groupBy(['u.id', 'u.first_name', 'u.last_name', 'l.geog'])
            .orderBy(sql`ST_Distance(
                l.geog::geography,
                ST_SetSRID(ST_MakePoint(${siteLocation.longitude}, ${siteLocation.latitude}), 4326)::geography
            )`, 'asc')
            .limit(10)
            .execute()
            .then(results => results.map(r => r.result));

        // Add profile picture URLs
        return await Promise.all(nearbySapsRaw.map(async sap => {
            let profile_picture_url;
            if (sap.profile_image_bucket_name && 
                sap.profile_image_owner_id && 
                sap.profile_image_file_name) {
                profile_picture_url = await FileService.getSignedFileUrl(
                    sap.profile_image_bucket_name,
                    `${sap.profile_image_owner_id}/${sap.profile_image_file_name}`
                );
            }
            
            return {
                ...sap,
                profile_picture_url,
                estimated_arrival_mins: Math.round(sap.distance_miles * 2) // Rough estimate: 30mph average speed
            };
        }));
    }

    /**
     * Create a work order
     */
    static async createWorkOrder(params: {
        site_id: SiteId;
        site_manager_id: UserId;
    }) {
        const [workOrder] = await db
            .insertInto('po105_job.work_order')
            .values({
                site_id: params.site_id,
                work_type: WorkTypeEnum.EMERGENCY,
                declaration_timestamp: new Date(),
                site_manager_id: params.site_manager_id,
                created_at: new Date(),
                updated_at: new Date()
            })
            .returning(['id'])
            .execute();

        return workOrder;
    }

    /**
     * Create a fault triage
     */
    static async createFaultTriage(params: {
        work_order_id: WorkOrderId;
        fault_type: FaultTypeEnum;
        severity: number;
        priority: number;
        description: string;
    }) {
        await db
            .insertInto('po105_job.fault_triage')
            .values({
                work_order_id: params.work_order_id,
                fault_type: params.fault_type,
                severity: params.severity,
                priority: params.priority,
                customer_description: params.description,
                submission_timestamp: new Date(),
                created_at: new Date(),
                updated_at: new Date()
            })
            .execute();
    }

    /**
     * Get site location and verify user access
     */
    static async getSiteLocation(siteId: SiteId, userId: UserId) {
        try {
            const site = await db
                .selectFrom('po105_management.site as s')
                .innerJoin('po105_management.site_manager as sm', join => 
                    join.onRef('sm.customer_id', '=', 's.customer_id')
                )
                .select([
                    's.id',
                    sql<any>`ST_AsGeoJSON(pl.geog)::jsonb`.as('geog')
                ])
                .where('s.id', '=', siteId)
                .where('sm.user_id', '=', userId)
                .where('s.deleted_at', 'is', null)
                .where('sm.deleted_at', 'is', null)
                .executeTakeFirst();

            return site || null;
        } catch (error: any) {
            console.error('ERROR in getSiteLocation:', error.message || error);
            throw error;
        }
    }

    /**
     * Get callout request status
     */
    static async getCalloutRequestStatus(calloutRequestId: CalloutRequestId, userId: UserId): Promise<{ response: boolean } | null> {
        try {
            // First verify the user has access to this callout request
            const hasAccess = await db
                .selectFrom('po105_job.callout_request as cr')
                .innerJoin('po105_job.work_order as wo', 'cr.work_order_id', 'wo.id')
                .select(['cr.id'])
                .where('cr.id', '=', calloutRequestId)
                .where('wo.site_manager_id', '=', userId)
                .where('cr.deleted_at', 'is', null)
                .executeTakeFirst();

            if (!hasAccess) return null;

            // Check if there's a callout response
            const response = await db
                .selectFrom('po105_job.callout_response')
                .select(['response'])
                .where('callout_request_id', '=', calloutRequestId)
                .where('deleted_at', 'is', null)
                .orderBy('response_timestamp', 'desc')
                .limit(1)
                .executeTakeFirst();

            return response || null;
        } catch (error: any) {
            console.error('ERROR in getCalloutRequestStatus:', error.message || error);
            throw error;
        }
    }

    /**
     * Get active callout for a work order
     */
    static async getActiveCallout(workOrderId: WorkOrderId, userId: UserId): Promise<{ id: CalloutId } | null> {
        try {
            // First verify the user has access to this work order
            const hasAccess = await db
                .selectFrom('po105_job.work_order')
                .select(['id'])
                .where('id', '=', workOrderId)
                .where('site_manager_id', '=', userId)
                .where('deleted_at', 'is', null)
                .executeTakeFirst();

            if (!hasAccess) return null;

            const callout = await db
                .selectFrom('po105_job.callout as c')
                .innerJoin('po105_job.callout_request as cr', 'c.callout_request_id', 'cr.id')
                .select(['c.id'])
                .where('cr.work_order_id', '=', workOrderId)
                .where('c.deleted_at', 'is', null)
                .executeTakeFirst();

            return callout || null;
        } catch (error: any) {
            console.error('ERROR in getActiveCallout:', error.message || error);
            throw error;
        }
    }

    /**
     * Get existing callout
     */
    static async getExistingCallout(calloutRequestId: CalloutRequestId): Promise<{ id: CalloutId } | null> {
        try {
            const existingCallout = await db
                .selectFrom('po105_job.callout')
                .select(['id'])
                .where('callout_request_id', '=', calloutRequestId)
                .where('deleted_at', 'is', null)
                .executeTakeFirst();

            return existingCallout || null;
        } catch (error: any) {
            console.error('ERROR in getExistingCallout:', error.message || error);
            throw error;
        }
    }

    /**
     * Create a callout request
     */
    static async createCalloutRequest(params: {
        work_order_id: WorkOrderId;
        site_manager_id: UserId;
        sap_id: UserId;
        sapLocation: { distance_miles: number };
    }) {
        await db
            .insertInto('po105_job.callout_request')
            .values({
                work_order_id: params.work_order_id,
                site_manager_id: params.site_manager_id,
                sap_id: params.sap_id,
                issuance_timestamp: new Date(),
                expires_after: '4 hours',
                estimated_distance_miles: params.sapLocation.distance_miles.toFixed(2), // Convert to string with 2 decimal places
                estimated_time_mins: Math.round(params.sapLocation.distance_miles * 2), // Rough estimate: 30mph average speed
                created_at: new Date(),
                updated_at: new Date()
            })
            .execute();
    }

    static async getSapCalloutRequests(sapId: UserId) {
        try {
            return await db
                .selectFrom('po105_job.callout_request as cr')
                .innerJoin('po105_job.work_order as wo', 'cr.work_order_id', 'wo.id')
                .innerJoin('po105_management.site as s', 'wo.site_id', 's.id')
                .select(eb => [
                    eb.ref('cr.id').as('id'),
                    eb.ref('s.name').as('site_name'),
                    sql<string>`wo.additional_attributes->>'fault_type'`.as('fault_type'),
                    eb.ref('wo.declaration_timestamp').as('declaration_timestamp'),
                    eb.ref('cr.estimated_distance_miles').as('estimated_distance_miles'),
                    eb.ref('cr.estimated_time_mins').as('estimated_time_mins'),
                    eb.ref('cr.expires_after').as('expires_after'),
                ])
                .where('cr.sap_id', '=', sapId)
                .where('cr.deleted_at', 'is', null)
                .where('wo.deleted_at', 'is', null)
                .execute();
        } catch (error: any) {
            console.error('ERROR in getSapCalloutRequests:', error.message || error);
            throw error;
        }
    }

    static async getSapActiveCallouts(sapId: UserId) {
        try {
            return await db
                .selectFrom('po105_job.callout as c')
                .innerJoin('po105_job.callout_request as cr', 'c.callout_request_id', 'cr.id')
                .innerJoin('po105_job.work_order as wo', 'cr.work_order_id', 'wo.id')
                .innerJoin('po105_management.site as s', 'wo.site_id', 's.id')
                .leftJoin('po105_job.callout_completion as cc', 'c.id', 'cc.callout_id')
                .select(eb => [
                    eb.ref('c.id').as('id'),
                    eb.ref('s.name').as('site_name'),
                    sql<string>`wo.additional_attributes->>'fault_type'`.as('fault_type'),
                    eb.ref('wo.declaration_timestamp').as('declaration_timestamp'),
                    eb.ref('c.callout_start_timestamp').as('callout_start_timestamp'),
                    sql<'IN_PROGRESS' | 'COMPLETED'>`CASE 
                        WHEN cc.completion_timestamp IS NOT NULL THEN 'COMPLETED'::text 
                        ELSE 'IN_PROGRESS'::text 
                    END`.as('status'),
                ])
                .where('cr.sap_id', '=', sapId)
                .where('c.deleted_at', 'is', null)
                .where('cr.deleted_at', 'is', null)
                .where('wo.deleted_at', 'is', null)
                .execute();
        } catch (error: any) {
            console.error('ERROR in getSapActiveCallouts:', error.message || error);
            throw error;
        }
    }

    static async getSapActiveCalloutsLocations(sapId: UserId) {
        try {
            return await db
                .selectFrom('po105_job.callout as c')
                .innerJoin('po105_job.callout_request as cr', 'c.callout_request_id', 'cr.id')
                .innerJoin('po105_job.work_order as wo', 'cr.work_order_id', 'wo.id')
                .innerJoin('po105_management.site as s', 'wo.site_id', 's.id')
                .leftJoin('po105_job.callout_completion as cc', 'c.id', 'cc.callout_id')
                .select(eb => [
                    eb.ref('c.id').as('id'),
                    eb.ref('s.name').as('site_name'),
                    sql<string>`wo.additional_attributes->>'fault_type'`.as('fault_type'),
                    eb.ref('wo.declaration_timestamp').as('declaration_timestamp'),
                    eb.ref('c.callout_start_timestamp').as('callout_start_timestamp'),
                    sql<'IN_PROGRESS' | 'COMPLETED'>`CASE 
                        WHEN cc.completion_timestamp IS NOT NULL THEN 'COMPLETED'::text 
                        ELSE 'IN_PROGRESS'::text 
                    END`.as('status'),
                    sql<number>`ST_Y(pl.geog::geometry)`.as('latitude'),
                    sql<number>`ST_X(pl.geog::geometry)`.as('longitude')
                ])
                .where('cr.sap_id', '=', sapId)
                .where('c.deleted_at', 'is', null)
                .where('cr.deleted_at', 'is', null)
                .where('wo.deleted_at', 'is', null)
                .execute();
        } catch (error: any) {
            console.error('ERROR in getSapActiveCalloutsLocations:', error.message || error);
            throw error;
        }
    }
}

export default EmergencyQueries; 