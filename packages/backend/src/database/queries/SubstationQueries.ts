/**
 * Substation-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import { SelectQueryBuilder, sql } from 'kysely';
import type { SiteId } from '@shared/interfaces/entities/po105_management/Site';
import type {
    Substation,
    SubstationId,
} from '@shared/interfaces/entities/po105_management/Substation';
import type { SubstationDto } from '@shared/interfaces/dto/models/substation-dto';
import type { PermanentLocationDto } from '@shared/interfaces/dto/models/location-dto';
import InstalledEquipmentQueries from './InstalledEquipmentQueries';
import Database from '@shared/interfaces/entities/Database';

/* QUERIES ================================================================ */

class SubstationQueries {
    /**
     * Fetches all SubstationDto for a given site ID.
     */
    public static async getSubstationDtosBySiteId(
        siteId: SiteId,
        includeEquipment: boolean = true,
    ): Promise<SubstationDto[]> {
        const rawSubstations = await db
            .selectFrom('po105_management.substation as sub')
            .innerJoin(
                'po105_management.permanent_location as pl',
                'pl.id',
                'sub.perm_location_id',
            )
            .where('sub.site_id', '=', siteId)
            .where('sub.deleted_at', 'is', null)
            .select([
                'sub.id',
                'sub.reference',
                'sub.name',
                'sub.number',
                'pl.geog',
                'pl.w3w',
                'pl.address_line_1',
                'pl.address_line_2',
                'pl.city',
                'pl.country',
                'pl.postcode',
            ])
            .execute();

        if (!rawSubstations || rawSubstations.length === 0) {
            return [];
        }

        const resultDtos: SubstationDto[] = [];
        for (const rs of rawSubstations) {
            const equipment = includeEquipment
                ? await InstalledEquipmentQueries.getInstalledEquipmentDtosBySubstationId(
                      rs.id,
                  )
                : [];

            const permLocation: PermanentLocationDto = {
                geog: rs.geog as any,
                w3w: rs.w3w,
                address_line_1: rs.address_line_1,
                address_line_2: rs.address_line_2,
                city: rs.city,
                country: rs.country,
                postcode: rs.postcode,
            };

            resultDtos.push({
                id: rs.id as any,
                reference: rs.reference as any,
                name: rs.name as any,
                number: rs.number as any,
                perm_location: permLocation,
                equipment: equipment,
            });
        }
        return resultDtos;
    }

    // public static getSubstationQueryBySiteId(
    //     siteId: SiteId
    // ) : SelectQueryBuilder {
    //     return db
    //         .selectFrom('po105_management.substation as sub')
    //         .innerJoin('po105_management.permanent_location as pl', 'pl.id', 'sub.perm_location_id')
    //         .where('sub.site_id', '=', siteId)
    //         .where('sub.deleted_at', 'is', null)
    //         .select([
    //             'sub.id',
    //             'sub.reference',
    //             'sub.name',
    //             'sub.number',
    //             'pl.geog',
    //             'pl.w3w',
    //             'pl.address_line_1',
    //             'pl.address_line_2',
    //             'pl.city',
    //             'pl.country',
    //             'pl.postcode'
    //         ]);
    // }
}

export default SubstationQueries;
