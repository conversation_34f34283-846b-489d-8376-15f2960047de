/**
 * Company-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import { sql } from 'kysely';
import type { CompanyId } from '@shared/interfaces/entities/po105_management/Company';
import type { UserId } from '@shared/interfaces/entities/po105_core/User';
import type { CompanyProfileDto } from '@shared/interfaces/dto/models/comapny-dto';
import type { ImageDto } from '@shared/interfaces/dto/models/file-dto';
import type { PermanentLocationDto } from '@shared/interfaces/dto/models/location-dto';
import type { UserProfileDto } from '@shared/interfaces/dto/models/user-dto';
import FileService from '../../services/FileService';
import { FileId } from '@shared/interfaces/entities/po105_storage/File';
import { logger } from '@/utils/logger';

/* QUERIES ================================================================ */

class CompanyQueries {
    /**
     * Fetches the complete profile for a company by its ID.
     * Includes profile image, permanent location, and account manager details.
     */
    public static async getCompanyProfileDtoById(companyId: CompanyId): Promise<CompanyProfileDto | null> {
        const result = await db
            .selectFrom('po105_management.company as co')
            .leftJoin('po105_storage.file as cpf', 'cpf.id', 'co.profile_image_id')
            .leftJoin('po105_storage.image as cpimg', 'cpimg.file_id', 'cpf.id')
            .leftJoin('po105_storage.media as cpmedia', 'cpmedia.file_id', 'cpf.id')
            .leftJoin('po105_management.permanent_location as cpl', 'cpl.id', 'co.perm_location_id')
            .leftJoin('po105_core.user as amu', 'amu.id', 'co.account_manager_id')
            .where('co.id', '=', companyId)
            .where('co.deleted_at', 'is', null)
            .select([
                'co.id',
                'co.company_type',
                'co.name',
                'co.contact_email',
                'co.profile_image_id',
                'cpf.bucket_name as profile_image_bucket_name',
                'cpf.owner_id as profile_image_owner_id',
                'cpf.file_name as profile_image_file_name',
                'co.perm_location_id',
                'co.account_manager_id',
                'amu.user_type as account_manager_user_type',

                sql<Omit<ImageDto, 'signedUrl'> | null>`
                    CASE
                        WHEN co.profile_image_id IS NOT NULL AND cpf.id IS NOT NULL
                        THEN json_build_object(
                            'id', cpf.id,
                            'description', cpf.description,
                            'metadata', cpf.metadata,
                            'tags', cpf.tags,
                            'source', cpmedia.source,
                            'width', cpmedia.width,
                            'height', cpmedia.height,
                            'mime_type', cpimg.mime_type
                        )
                        ELSE NULL
                    END
                `.as('profile_image_details'),

                sql<PermanentLocationDto | null>`
                    CASE
                        WHEN co.perm_location_id IS NOT NULL AND cpl.id IS NOT NULL
                        THEN json_build_object(
                            'geog', ST_AsGeoJSON(cpl.geog)::json,
                            'w3w', cpl.w3w,
                            'address_line_1', cpl.address_line_1,
                            'address_line_2', cpl.address_line_2,
                            'city', cpl.city,
                            'country', cpl.country,
                            'postcode', cpl.postcode
                        )
                        ELSE NULL
                    END
                `.as('perm_location_details'),

                
                sql<Pick<UserProfileDto, 'id' | 'email' | 'first_name' | 'last_name'> | null>`
                    CASE
                        WHEN co.account_manager_id IS NOT NULL AND amu.id IS NOT NULL
                        THEN json_build_object(
                            'id', amu.id,
                            'email', amu.email,
                            'first_name', amu.first_name,
                            'last_name', amu.last_name
                        )
                        ELSE NULL
                    END
                `.as('account_manager_basic_details')
            ])
            .executeTakeFirst();

        if (!result) {
            return null;
        }

        let profileImageDto: ImageDto | undefined = undefined;
        if (result.profile_image_id && result.profile_image_details && 
            result.profile_image_bucket_name && result.profile_image_owner_id && 
            result.profile_image_file_name) {
            
            // Use direct URL signing with the data we already fetched from db
            const signedUrl = await FileService.getSignedFileUrl(
                result.profile_image_bucket_name as string,
                `${result.profile_image_owner_id}/${result.profile_image_file_name}`
            );
            
            profileImageDto = {
                ...result.profile_image_details,
                signedUrl: signedUrl ?? null,
            };
        }

        let accountManagerDto: UserProfileDto | undefined = undefined;
        if (result.account_manager_id && result.account_manager_basic_details) {
            const amDetails = result.account_manager_basic_details;
            accountManagerDto = {
                id: amDetails.id,
                email: amDetails.email!,
                phone_number: '',
                first_name: amDetails.first_name!,
                last_name: amDetails.last_name!,
                average_rating: null,
                total_ratings: null,
                user_type: result.account_manager_user_type!,
                profile_image: undefined as any,
            };
        }
        
        if (!result.name || !result.contact_email || !result.company_type) {
            logger.error(
                { 
                    companyId 
                }, 
                'Company is missing one or more required fields (name, contact_email, company_type).');
            return null;
        }

        const companyProfile: CompanyProfileDto = {
            id: result.id as any,
            company_type: result.company_type as any,
            name: result.name as any,
            contact_email: result.contact_email as any,
            profile_image: profileImageDto,
            perm_location: result.perm_location_details ?? undefined,
            account_manager: accountManagerDto,
        };

        return companyProfile;
    }

    /**
     * Fetches complete profiles for multiple companies with optimized batch URL signing.
     * @param companyIds - Array of company IDs to fetch profiles for
     * @returns Array of company profiles
     */
    public static async getCompanyProfileDtosByIds(companyIds: CompanyId[]): Promise<CompanyProfileDto[]> {
        if (!companyIds.length) return [];
        
        const results = await db
            .selectFrom('po105_management.company as co')
            .leftJoin('po105_storage.file as cpf', 'cpf.id', 'co.profile_image_id')
            .leftJoin('po105_storage.image as cpimg', 'cpimg.file_id', 'cpf.id')
            .leftJoin('po105_storage.media as cpmedia', 'cpmedia.file_id', 'cpf.id')
            .leftJoin('po105_management.permanent_location as cpl', 'cpl.id', 'co.perm_location_id')
            .leftJoin('po105_core.user as amu', 'amu.id', 'co.account_manager_id')
            .where('co.id', 'in', companyIds)
            .where('co.deleted_at', 'is', null)
            .select([
                'co.id',
                'co.company_type',
                'co.name',
                'co.contact_email',
                'co.profile_image_id',
                'cpf.bucket_name as profile_image_bucket_name',
                'cpf.owner_id as profile_image_owner_id',
                'cpf.file_name as profile_image_file_name',
                'co.perm_location_id',
                'co.account_manager_id',
                'amu.user_type as account_manager_user_type',

                sql<Omit<ImageDto, 'signedUrl'> | null>`
                    CASE
                        WHEN co.profile_image_id IS NOT NULL AND cpf.id IS NOT NULL
                        THEN json_build_object(
                            'id', cpf.id,
                            'description', cpf.description,
                            'metadata', cpf.metadata,
                            'tags', cpf.tags,
                            'source', cpmedia.source,
                            'width', cpmedia.width,
                            'height', cpmedia.height,
                            'mime_type', cpimg.mime_type
                        )
                        ELSE NULL
                    END
                `.as('profile_image_details'),

                sql<PermanentLocationDto | null>`
                    CASE
                        WHEN co.perm_location_id IS NOT NULL AND cpl.id IS NOT NULL
                        THEN json_build_object(
                            'geog', ST_AsGeoJSON(cpl.geog)::json,
                            'w3w', cpl.w3w,
                            'address_line_1', cpl.address_line_1,
                            'address_line_2', cpl.address_line_2,
                            'city', cpl.city,
                            'country', cpl.country,
                            'postcode', cpl.postcode
                        )
                        ELSE NULL
                    END
                `.as('perm_location_details'),

                
                sql<Pick<UserProfileDto, 'id' | 'email' | 'first_name' | 'last_name'> | null>`
                    CASE
                        WHEN co.account_manager_id IS NOT NULL AND amu.id IS NOT NULL
                        THEN json_build_object(
                            'id', amu.id,
                            'email', amu.email,
                            'first_name', amu.first_name,
                            'last_name', amu.last_name
                        )
                        ELSE NULL
                    END
                `.as('account_manager_basic_details')
            ])
            .execute();
            
        if (!results.length) {
            return [];
        }
        
        // Extract file metadata in the format needed for URL signing
        const fileMetadata = results
            .filter(r => r.profile_image_id !== null 
                && r.profile_image_bucket_name 
                && r.profile_image_owner_id 
                && r.profile_image_file_name)
            .map(r => ({
                id: r.profile_image_id as FileId,
                bucket_name: r.profile_image_bucket_name as string,
                owner_id: r.profile_image_owner_id as string,
                file_name: r.profile_image_file_name as string
            }));
            
        // Group files by bucket name since Supabase requires files to be in the same bucket
        const bucketGroups: Record<string, { id: FileId, bucket_name: string, owner_id: string, file_name: string }[]> = {};
        
        fileMetadata.forEach(file => {
            if (!bucketGroups[file.bucket_name]) {
                bucketGroups[file.bucket_name] = [];
            }
            bucketGroups[file.bucket_name].push({
                id: file.id,    
                bucket_name: file.bucket_name,
                owner_id: file.owner_id,
                file_name: file.file_name
            });
        });
        
        // Create a map to store all signed URLs
        const urlMap = new Map<string, string>();
        
        // Process each bucket group separately
        for (const [bucketName, files] of Object.entries(bucketGroups)) {
            if (files.length === 0) continue;
            
            const paths = files.map(f => `${f.owner_id}/${f.file_name}`);
            const signedUrls = await FileService.getSignedFileUrls(bucketName, paths);
            
            // Map the results back to file IDs
            files.forEach((file, index) => {
                if (index < signedUrls.length) {
                    urlMap.set(file.id as string, signedUrls[index]);
                }
            });
        }
        
        // Build company profiles
        const companyProfiles: CompanyProfileDto[] = [];
        
        for (const result of results) {
            // Skip companies with missing required fields
            if (!result.name || !result.contact_email || !result.company_type) {
                logger.error(
                    { companyId: result.id }, 
                    'Company is missing one or more required fields (name, contact_email, company_type).');
                continue;
            }
            
            let profileImageDto: ImageDto | undefined = undefined;
            if (result.profile_image_id && result.profile_image_details) {
                const signedUrl = urlMap.get(result.profile_image_id as string);
                profileImageDto = {
                    ...result.profile_image_details,
                    signedUrl: signedUrl ?? null,
                };
            }

            let accountManagerDto: UserProfileDto | undefined = undefined;
            if (result.account_manager_id && result.account_manager_basic_details) {
                const amDetails = result.account_manager_basic_details;
                accountManagerDto = {
                    id: amDetails.id,
                    email: amDetails.email!,
                    phone_number: '',
                    first_name: amDetails.first_name!,
                    last_name: amDetails.last_name!,
                    average_rating: null,
                    total_ratings: null,
                    user_type: result.account_manager_user_type!,
                    profile_image: undefined as any,
                };
            }
            
            const companyProfile: CompanyProfileDto = {
                id: result.id as any,
                company_type: result.company_type as any,
                name: result.name as any,
                contact_email: result.contact_email as any,
                profile_image: profileImageDto,
                perm_location: result.perm_location_details ?? undefined,
                account_manager: accountManagerDto,
            };
            
            companyProfiles.push(companyProfile);
        }
        
        return companyProfiles;
    }
}

export default CompanyQueries; 