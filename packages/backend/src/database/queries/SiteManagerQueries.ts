/**
 * Site Manager-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import type { UserId } from '@shared/interfaces/entities/po105_core/User';
import type { SiteManagerProfileDto } from '@shared/interfaces/dto/models/site-manager-dto';
import type { CustomerId } from '@shared/interfaces/entities/po105_management/Customer';
import UserQueries from './UserQueries';
import CompanyQueries from './CompanyQueries';
import SiteQueries from './SiteQueries';
import { CompanyId } from '@shared/interfaces/entities/po105_management/Company';

/* QUERIES ================================================================ */

class SiteManagerQueries {
    
    public static async getFullProfile(
        userId: UserId
    ): Promise<SiteManagerProfileDto | null> {
        const userProfile = await UserQueries.getUserProfileDtoById(userId);
        
        if (!userProfile) {
            // No user profile found
            return null;
        }

        // Get customer_id for this Site Manager
        const userRecord = await db
            .selectFrom('po105_management.site_manager as sm')
            .select(['sm.customer_id'])
            .leftJoin('po105_management.customer as c', 'sm.customer_id', 'c.id')
            .select(['c.company_id as company_id'])
            .where('sm.user_id', '=', userId)  
            .executeTakeFirst();

        if (!userRecord || !userRecord.customer_id) {
            return null;
        }
        const customerId = userRecord.customer_id as CustomerId;
        const companyId = userRecord.company_id as CompanyId;

        // Get company profile DTO
        const companyProfile = await CompanyQueries.getCompanyProfileDtoById(companyId);
        if (!companyProfile) {
            // No company profile found
            return null;
        }

        // Fetch Sites for the customer
        const includeSubstations = true;
        const includeEquipment = false;
        const sites = await SiteQueries.getSiteDtosByCustomerId(
            customerId, 
            includeSubstations, 
            includeEquipment
        );

        // Assemble SiteManagerProfileDto
        const siteManagerProfile: SiteManagerProfileDto = {
            ...userProfile,
            company: companyProfile,
            sites: sites,
        };

        return siteManagerProfile;
    }
}

export default SiteManagerQueries; 
