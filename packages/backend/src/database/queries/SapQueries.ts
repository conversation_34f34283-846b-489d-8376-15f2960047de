/**
 * SAP-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import { sql } from 'kysely';
import type { UserId } from '@shared/interfaces/entities/po105_core/User';
import type { SapDto } from '@shared/interfaces/dto/models/sap-dto';
import type { UserProfileDto } from '@shared/interfaces/dto/models/user-dto';
import type { CompanyProfileDto } from '@shared/interfaces/dto/models/comapny-dto';
import type {
    DocumentDto,
    ImageDto,
} from '@shared/interfaces/dto/models/file-dto';
import type { PermanentLocationDto } from '@shared/interfaces/dto/models/location-dto';
import UserTypeEnum from '@shared/interfaces/entities/po105_core/UserTypeEnum';
import { FileId } from '@shared/interfaces/entities/po105_storage/File';
import { logger } from '../../utils/logger';
import fileQueries from './FileQueries';
import FileService from '../../services/FileService';

/* CLASS =================================================================== */

/**
 * SAP-related database queries
 */
class SapQueries {
    async getFullProfile(userId: UserId): Promise<SapDto | null> {
        const result = await db
            .selectFrom('po105_core.sap as s')
            .innerJoin('po105_core.user as u', 'u.id', 's.user_id')
            .leftJoin('po105_storage.file as pf', 'pf.id', 'u.profile_image_id')
            .leftJoin('po105_storage.image as pimg', 'pimg.file_id', 'pf.id')
            .leftJoin(
                'po105_storage.media as pmedia',
                'pmedia.file_id',
                'pf.id',
            )
            .leftJoin(
                'po105_management.permanent_location as sap_pl',
                'sap_pl.id',
                's.perm_location_id',
            )
            .leftJoin('po105_management.partner as p', 'p.id', 's.partner_id')
            .leftJoin('po105_management.company as pc', 'pc.id', 'p.company_id')
            .leftJoin(
                'po105_management.permanent_location as pc_pl',
                'pc_pl.id',
                'pc.perm_location_id',
            )
            .where('s.user_id', '=', userId)
            .select([
                'u.id as user_id',
                'u.user_type',
                'u.email',
                'u.first_name',
                'u.last_name',
                'u.phone_number',
                'u.average_rating',
                'u.total_ratings',
                'u.profile_image_id',
                'pf.bucket_name as profile_image_bucket_name',
                'pf.owner_id as profile_image_owner_id',
                'pf.file_name as profile_image_file_name',

                // Profile image details
                sql<Omit<ImageDto, 'signedUrl'> | null>`
                    CASE
                        WHEN u.profile_image_id IS NOT NULL AND pf.id IS NOT NULL
                        THEN json_build_object(
                            'id', pf.id,
                            'description', pf.description,
                            'metadata', pf.metadata,
                            'tags', pf.tags,
                            'source', pmedia.source,
                            'width', pmedia.width,
                            'height', pmedia.height,
                            'mime_type', pimg.mime_type
                        )
                        ELSE NULL
                    END
                `.as('profile_image_details'),

                's.qualification',
                's.availability',
                's.total_callouts',
                's.sla_file_id',
                's.perm_location_id',
                's.partner_id',
                'sap_pl.id as sap_pl_id',
                'sap_pl.address_line_1 as sap_pl_address_line_1',
                'sap_pl.address_line_2 as sap_pl_address_line_2',
                'sap_pl.city as sap_pl_city',
                'sap_pl.country as sap_pl_country',
                'sap_pl.postcode as sap_pl_postcode',
                'sap_pl.w3w as sap_pl_w3w',
                sql<any>`ST_AsGeoJSON(sap_pl.geog)::json`.as('sap_pl_geog'),
                'p.id as p_id',
                'p.sla_file_id as partner_sla_file_id',
                'pc.id as pc_id',
                'pc.company_type as pc_company_type',
                'pc.name as pc_name',
                'pc.contact_email as pc_contact_email',
                'pc.profile_image_id as pc_profile_image_id',
                'pc.perm_location_id as pc_perm_location_id',
                'pc_pl.id as pc_pl_id',
                'pc_pl.address_line_1 as pc_pl_address_line_1',
                'pc_pl.address_line_2 as pc_pl_address_line_2',
                'pc_pl.city as pc_pl_city',
                'pc_pl.country as pc_pl_country',
                'pc_pl.postcode as pc_pl_postcode',
                'pc_pl.w3w as pc_pl_w3w',
                sql<any>`ST_AsGeoJSON(pc_pl.geog)::json`.as('pc_pl_geog'),
            ])
            .executeTakeFirst();

        if (!result) {
            logger.warn(
                { userId },
                'SAP user data not found for getFullProfile.',
            );
            return null;
        }

        let profileImageDto: ImageDto | undefined;
        if (
            result.profile_image_id &&
            result.profile_image_details &&
            result.profile_image_bucket_name &&
            result.profile_image_owner_id &&
            result.profile_image_file_name
        ) {
            // Use direct URL signing with data we already have
            const signedUrl = await FileService.getSignedFileUrl(
                result.profile_image_bucket_name,
                `${result.profile_image_owner_id}/${result.profile_image_file_name}`,
            );

            if (!signedUrl) {
                logger.warn(
                    { userId },
                    'Could not generate signed URL for profile image of SAP.',
                );
                return null;
            }

            profileImageDto = {
                ...result.profile_image_details,
                signedUrl: signedUrl,
            };
        }

        const userProfileDto: UserProfileDto = {
            id: result.user_id as UserId,
            user_type: result.user_type as UserTypeEnum,
            email: result.email as string,
            first_name: result.first_name as string,
            last_name: result.last_name as string,
            phone_number: result.phone_number as string,
            average_rating: result.average_rating as string | null,
            total_ratings: result.total_ratings as number | null,
            profile_image: profileImageDto,
        };

        const sapSlaFile = (await fileQueries.getFileDtoById(
            result.sla_file_id as FileId | null,
        )) as DocumentDto | undefined;

        let sapPermanentLocationDto: PermanentLocationDto | undefined =
            undefined;
        if (result.sap_pl_id && result.sap_pl_geog != null) {
            sapPermanentLocationDto = {
                address_line_1: result.sap_pl_address_line_1 as string,
                address_line_2: result.sap_pl_address_line_2 as string | null,
                city: result.sap_pl_city as string,
                country: result.sap_pl_country as string,
                postcode: result.sap_pl_postcode as string | null,
                w3w: result.sap_pl_w3w as string | null,
                geog: result.sap_pl_geog,
            };
        }

        let partnerDtoPart: SapDto['partner'] = undefined;
        if (result.p_id && result.pc_id) {
            const partnerCompanyProfileImageDto =
                (await fileQueries.getFileDtoById(
                    result.pc_profile_image_id as FileId | null,
                )) as ImageDto | null;

            let companyPermLocationDto: PermanentLocationDto | undefined =
                undefined;
            if (result.pc_pl_id && result.pc_pl_geog != null) {
                companyPermLocationDto = {
                    address_line_1: result.pc_pl_address_line_1 as string,
                    address_line_2: result.pc_pl_address_line_2 as
                        | string
                        | null,
                    city: result.pc_pl_city as string,
                    country: result.pc_pl_country as string,
                    postcode: result.pc_pl_postcode as string | null,
                    w3w: result.pc_pl_w3w as string | null,
                    geog: result.pc_pl_geog,
                };
            }

            const companyProfileDto: CompanyProfileDto = {
                id: result.pc_id as any,
                company_type: result.pc_company_type as any,
                name: result.pc_name as any,
                contact_email: result.pc_contact_email as any,
                profile_image: partnerCompanyProfileImageDto as
                    | ImageDto
                    | undefined,
                perm_location: companyPermLocationDto,
            };

            const partnerSlaFile = (await fileQueries.getFileDtoById(
                result.partner_sla_file_id as FileId | null,
            )) as DocumentDto | undefined;

            partnerDtoPart = {
                company: companyProfileDto,
                sla_file: partnerSlaFile,
            };
        }

        const sapDto: SapDto = {
            ...userProfileDto,
            qualification: result.qualification as string | null,
            availability: result.availability as boolean,
            total_callouts: result.total_callouts as number | null,
            partner: partnerDtoPart,
            sla_file: sapSlaFile,
            permanent_location: sapPermanentLocationDto,
        };

        return sapDto;
    }
}

export default new SapQueries();
