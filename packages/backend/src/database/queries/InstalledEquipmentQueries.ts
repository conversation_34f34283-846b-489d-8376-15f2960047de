/**
 * Installed Equipment-related database queries
 */

/* IMPORTS ================================================================ */

import { db } from '../connection';
import { sql } from 'kysely';
import type { SubstationId } from '@shared/interfaces/entities/po105_management/Substation';
import type { InstalledEquipmentDto, InstalledEquipmentImage } from '@shared/interfaces/dto/models/equipment-dto';
import type { DocumentDto } from '@shared/interfaces/dto/models/file-dto'; 
import FileService from '../../services/FileService';
import type { FileId } from '@shared/interfaces/entities/po105_storage/File';

/* TYPES =================================================================== */

// Extended DocumentDto with bucket information needed for URL signing
type DocumentDtoWithBucket = Omit<DocumentDto, 'signedUrl'> & {
    bucket_name: string;
    bucket_id: string;
};

/* QUERIES ================================================================ */

class InstalledEquipmentQueries {
    /**
     * Fetches all InstalledEquipmentDto for a given substation ID.
     */
    public static async getInstalledEquipmentDtosBySubstationId(
        substationId: SubstationId
    ): Promise<InstalledEquipmentDto[]> {

        const installedEquipmentList = await db
            .selectFrom('po105_management.installed_equipment as ie')
            .innerJoin('po105_management.equipment as eq', 'eq.id', 'ie.equipment_id')
            // Spec File Joins
            .leftJoin('po105_storage.file as sf', 'sf.id', 'eq.spec_file_id')
            .leftJoin('po105_storage.document as sdoc', 'sdoc.file_id', 'sf.id')
            .where('ie.substation_id', '=', substationId)
            .where('ie.deleted_at', 'is', null)
            .where('eq.deleted_at', 'is', null)
            .select([
                // InstalledEquipment specific fields
                'ie.id as installed_equipment_id',
                'ie.placement',
                'ie.serial_number',
                'ie.commissioning_date',
                'ie.last_inspection_date',
                
                'eq.id as equipment_id',
                'eq.category',
                'eq.category_other',
                'eq.reference',
                'eq.manufacturer',
                'eq.eq_type',
                'eq.manufacturing_date',
                'eq.system_voltage',
                'eq.current_rating',
                'eq.spec_file_id',
                'eq.additional_attributes',

                sql<DocumentDtoWithBucket | null>`
                    CASE WHEN eq.spec_file_id IS NOT NULL AND sf.id IS NOT NULL
                        THEN json_build_object(
                            'id', sf.id,
                            'description', sf.description,
                            'metadata', sf.metadata,
                            'tags', sf.tags,
                            'mime_type', sdoc.mime_type,
                            'bucket_name', sf.bucket_name,
                            'bucket_id', sf.bucket_id
                        )
                        ELSE NULL
                    END
                `.as('spec_file_details'),

                sql<Array<
                    Omit<InstalledEquipmentImage, 'signedUrl' | 'id'> & 
                    { id: FileId, bucket_name: string, owner_id: string, file_name: string }> 
                    | null
                    >`(
                    SELECT json_agg(
                        json_build_object(
                            'id', imgf.id,        
                            'description', imge.description, 
                            'capture_timestamp', imge.capture_timestamp,
                            'metadata', imgf.metadata, 
                            'tags', imgf.tags, 
                            'source', imgm.source,
                            'width', imgm.width,
                            'height', imgm.height,
                            'mime_type', imgi.mime_type,
                            'bucket_name', imgf.bucket_name,
                            'owner_id', imgf.owner_id,
                            'file_name', imgf.file_name
                        )
                    )
                    FROM po105_management.installed_equipment_image as imge
                    INNER JOIN po105_storage.file as imgf ON imgf.id = imge.image_id
                    INNER JOIN po105_storage.image as imgi ON imgi.file_id = imgf.id
                    INNER JOIN po105_storage.media as imgm ON imgm.file_id = imgf.id
                    WHERE imge.installed_equipment_id = ie.id AND imgf.deleted_at IS NULL
                )`.as('images_details_raw')
            ])
            .execute();

        if (!installedEquipmentList || installedEquipmentList.length === 0) {
            return [];
        }

        const resultDtos: InstalledEquipmentDto[] = [];

        for (const ieRaw of installedEquipmentList) {
            let specFileDto: DocumentDto | undefined = undefined;
            if (ieRaw.spec_file_id && ieRaw.spec_file_details) {
                const specFileWithBucket = ieRaw.spec_file_details as DocumentDtoWithBucket;
                const signedUrl = await FileService.getSignedFileUrl(
                    specFileWithBucket.bucket_name,
                    specFileWithBucket.bucket_id
                );
                specFileDto = { ...ieRaw.spec_file_details, signedUrl: signedUrl ?? null };
            }

            // Optimize image URL signing by grouping by bucket_name (batch signing)
            const equipmentImages: InstalledEquipmentImage[] = [];
            if (ieRaw.images_details_raw && ieRaw.images_details_raw.length > 0) {
                const imageIndexMap = new Map<string, number>();
                
                const bucketGroups: Record<string, { index: number, path: string }[]> = {};
                
                ieRaw.images_details_raw.forEach((imgRaw, index) => {
                    if (!imgRaw.bucket_name || !imgRaw.owner_id || !imgRaw.file_name) {
                        return;
                    }
                    
                    const objectPath = `${imgRaw.owner_id}/${imgRaw.file_name}`;
                    
                    const uniqueId = `${index}-${objectPath}`;
                    imageIndexMap.set(uniqueId, index);
                    
                    if (!bucketGroups[imgRaw.bucket_name]) {
                        bucketGroups[imgRaw.bucket_name] = [];
                    }
                    
                    bucketGroups[imgRaw.bucket_name].push({
                        index: index,
                        path: objectPath
                    });
                });
                
                const signedUrlMap = new Map<number, string>();
                
                for (const [bucketName, files] of Object.entries(bucketGroups)) {
                    if (files.length === 0) continue;
                    
                    const paths = files.map(f => f.path);
                    const signedUrls = await FileService.getSignedFileUrls(bucketName, paths);
                    
                    files.forEach((file, arrayIndex) => {
                        if (arrayIndex < signedUrls.length) {
                            signedUrlMap.set(file.index, signedUrls[arrayIndex]);
                        }
                    });
                }
                
                for (let i = 0; i < ieRaw.images_details_raw.length; i++) {
                    const imgRaw = ieRaw.images_details_raw[i];
                    const signedUrl = signedUrlMap.get(i);
                    
                    equipmentImages.push({
                        ...(imgRaw as Omit<InstalledEquipmentImage, 'signedUrl'>),
                        signedUrl: signedUrl ?? null,
                    });
                }
            }
            
            resultDtos.push({
                id: ieRaw.equipment_id as any, 
                category: ieRaw.category as any,
                category_other: ieRaw.category_other as any,
                reference: ieRaw.reference as any,
                manufacturer: ieRaw.manufacturer as any,
                eq_type: ieRaw.eq_type as any,
                manufacturing_date: ieRaw.manufacturing_date as any,
                system_voltage: ieRaw.system_voltage as any,
                current_rating: ieRaw.current_rating as any,
                spec_file_id: ieRaw.spec_file_id as any,
                additional_attributes: ieRaw.additional_attributes as any,
                spec_file: specFileDto,
                placement: ieRaw.placement as any,
                serial_number: ieRaw.serial_number as any,
                commissioning_date: ieRaw.commissioning_date as any,
                last_inspection_date: ieRaw.last_inspection_date as any,
                images: equipmentImages,
            });
        }
        return resultDtos;
    }
}

export default InstalledEquipmentQueries; 