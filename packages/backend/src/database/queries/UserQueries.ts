/**
 * User-related database queries
 */


/* IMPORTS ================================================================ */

import { UserId } from '@shared/interfaces/entities/po105_core/User';
import { db } from '../connection';
import { sql } from 'kysely';
import type { UserProfileDto } from '@shared/interfaces/dto/models/user-dto';
import type { ImageDto } from '@shared/interfaces/dto/models/file-dto';
import FileService from '../../services/FileService';
import { FileId } from '@shared/interfaces/entities/po105_storage/File';

/* QUERIES ================================================================ */

class UserQueries {
    
    /**
     * Fetches the complete profile for a user by their ID.
     */
    public static async getUserProfileDtoById(userId: UserId): Promise<UserProfileDto | null> {
        const result = await db
            .selectFrom('po105_core.user as u')
            .leftJoin('po105_storage.file as pf', 'pf.id', 'u.profile_image_id')
            .leftJoin('po105_storage.image as pimg', 'pimg.file_id', 'pf.id')
            .leftJoin('po105_storage.media as pmedia', 'pmedia.file_id', 'pf.id')
            .where('u.id', '=', userId)
            .where('u.deleted_at', 'is', null)
            .select([
                'u.id',
                'u.email',
                'u.phone_number',
                'u.first_name',
                'u.last_name',
                'u.average_rating',
                'u.total_ratings',
                'u.user_type',
                'u.profile_image_id',
                'pf.bucket_name as profile_image_bucket_name',
                'pf.owner_id as profile_image_owner_id',
                'pf.file_name as profile_image_file_name',
                sql<Omit<ImageDto, 'signedUrl'> | null>`
                    CASE
                        WHEN u.profile_image_id IS NOT NULL AND pf.id IS NOT NULL
                        THEN json_build_object(
                            'id', pf.id,
                            'description', pf.description,
                            'metadata', pf.metadata,
                            'tags', pf.tags,
                            'source', pmedia.source,
                            'width', pmedia.width,
                            'height', pmedia.height,
                            'mime_type', pimg.mime_type
                        )
                        ELSE NULL
                    END
                `.as('profile_image_details')
            ])
            .executeTakeFirst();

        if (!result) {
            return null;
        }

        let profileImageDto: ImageDto | undefined;
        if (result.profile_image_id && result.profile_image_details && 
            result.profile_image_bucket_name && result.profile_image_owner_id && 
            result.profile_image_file_name) {
            
            // Use direct URL signing with data we already have
            const signedUrl = await FileService.getSignedFileUrl(
                result.profile_image_bucket_name, 
                `${result.profile_image_owner_id}/${result.profile_image_file_name}`
            );
            
            if (!signedUrl) {
                console.warn(`Could not generate signed URL for profile image of user ${userId}.`);
                return null;
            }
            
            profileImageDto = {
                ...result.profile_image_details,
                signedUrl: signedUrl,
            };
        }

        const userProfile: UserProfileDto = {
            id: result.id,
            email: result.email,
            phone_number: result.phone_number,
            first_name: result.first_name,
            last_name: result.last_name,
            average_rating: result.average_rating,
            total_ratings: result.total_ratings,
            user_type: result.user_type,
            profile_image: profileImageDto,
        };

        return userProfile;
    }
    
    /**
     * Fetches multiple user profiles with optimized batch URL signing.
     * @param userIds - Array of user IDs to fetch profiles for
     * @returns Array of user profiles
     */
    public static async getUserProfileDtosByIds(userIds: UserId[]): Promise<UserProfileDto[]> {
        if (!userIds.length) return [];
        
        const results = await db
            .selectFrom('po105_core.user as u')
            .leftJoin('po105_storage.file as pf', 'pf.id', 'u.profile_image_id')
            .leftJoin('po105_storage.image as pimg', 'pimg.file_id', 'pf.id')
            .leftJoin('po105_storage.media as pmedia', 'pmedia.file_id', 'pf.id')
            .where('u.id', 'in', userIds)
            .where('u.deleted_at', 'is', null)
            .select([
                'u.id',
                'u.email',
                'u.phone_number',
                'u.first_name',
                'u.last_name',
                'u.average_rating',
                'u.total_ratings',
                'u.user_type',
                'u.profile_image_id',
                'pf.bucket_name as profile_image_bucket_name',
                'pf.owner_id as profile_image_owner_id',
                'pf.file_name as profile_image_file_name',
                sql<Omit<ImageDto, 'signedUrl'> | null>`
                    CASE
                        WHEN u.profile_image_id IS NOT NULL AND pf.id IS NOT NULL
                        THEN json_build_object(
                            'id', pf.id,
                            'description', pf.description,
                            'metadata', pf.metadata,
                            'tags', pf.tags,
                            'source', pmedia.source,
                            'width', pmedia.width,
                            'height', pmedia.height,
                            'mime_type', pimg.mime_type
                        )
                        ELSE NULL
                    END
                `.as('profile_image_details')
            ])
            .execute();
            
        if (!results.length) {
            return [];
        }
        
        // Extract file metadata directly from the query results
        const fileMetadata = results
            .filter(r => r.profile_image_id !== null && 
                r.profile_image_bucket_name && 
                r.profile_image_owner_id && 
                r.profile_image_file_name)
            .map(r => ({
                id: r.profile_image_id as FileId,
                bucket_name: r.profile_image_bucket_name as string,
                owner_id: r.profile_image_owner_id as string,
                file_name: r.profile_image_file_name as string
            }));
            
        // Group files by bucket name since Supabase requires files to be in the same bucket
        const bucketGroups: Record<string, { 
            id: FileId, 
            bucket_name: string, 
            owner_id: string, 
            file_name: string 
        }[]> = {};
        
        fileMetadata.forEach(file => {
            if (!bucketGroups[file.bucket_name]) {
                bucketGroups[file.bucket_name] = [];
            }
            bucketGroups[file.bucket_name].push({
                id: file.id,
                bucket_name: file.bucket_name,
                owner_id: file.owner_id,
                file_name: file.file_name
            });
        });
        
        // Create a map to store all signed URLs
        const urlMap = new Map<string, string>();
        
        // Process each bucket group separately
        for (const [bucketName, files] of Object.entries(bucketGroups)) {
            if (files.length === 0) continue;
            
            const paths = files.map(f => `${f.owner_id}/${f.file_name}`);
            const signedUrls = await FileService.getSignedFileUrls(
                bucketName, 
                paths
            );
            
            // Map the results back to file IDs
            files.forEach((file, index) => {
                if (index < signedUrls.length) {
                    urlMap.set(file.id as string, signedUrls[index]);
                }
            });
        }
        
        // Construct user profiles with signed URLs
        const userProfiles: UserProfileDto[] = results.map(result => {
            let profileImageDto: ImageDto | undefined = undefined;
            
            if (result.profile_image_id && result.profile_image_details) {
                const signedUrl = urlMap.get(result.profile_image_id as string);
                
                profileImageDto = {
                    ...result.profile_image_details,
                    signedUrl: signedUrl ?? null,
                };
            }
            
            return {
                id: result.id,
                email: result.email,
                phone_number: result.phone_number,
                first_name: result.first_name,
                last_name: result.last_name,
                average_rating: result.average_rating,
                total_ratings: result.total_ratings,
                user_type: result.user_type,
                profile_image: profileImageDto,
            };
        });
        
        return userProfiles;
    }
}

export default UserQueries;

