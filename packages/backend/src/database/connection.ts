/* IMPORTS ================================================================= */

import { Kysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';
import Database from '@shared/interfaces/entities/Database';
import { DatabaseError, InternalError } from '@/models/errors';
import ErrorCodes from '@/constants/ErrorCodes';
import { createClient } from '@supabase/supabase-js';
import { 
    DB_CONNECTION_STRING,
    DB_MAX_POOL_SIZE,
    SUPABASE_URL, 
    SUPABASE_SERVICE_KEY 
} from '@/config/env';

/* TYPES =================================================================== */

export type PrimaryDb = Kysely<Database>;

/* SUPABASE CLIENT & CONFIGURATION ========================================= */

export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);    

// Construct storage URL for authenticated access
export const STORAGE_URL = `${SUPABASE_URL}/storage/v1/object/authenticated`;

/* DATABASE CONNECTION ===================================================== */

function createKyselyDb(): Kysely<Database> {
    // wrapping this in a function so we can throw an error upon failure
    try {
        return new Kysely<Database>({
            dialect: new PostgresDialect({
                pool: new Pool({
                    connectionString: DB_CONNECTION_STRING,
                    max: DB_MAX_POOL_SIZE,
                }),
            }),
        });
    }
    catch (dbErr: any) {
        throw new DatabaseError(ErrorCodes.DATABASE_INITIALIZATION_FAILED, 
            dbErr);
    }
}

export const db: Kysely<Database> = createKyselyDb();

// Cleanup connections
export const destroyConnections = async (): Promise<void> => {
    try {
        if (db) {
            await db.destroy();
        }
    } catch (error: any) {
        throw new InternalError(ErrorCodes.DATABASE_CLEANUP_FAILED, error);
    }
};

/* QUERYING METHODS ======================================================== */

// export const query = async <T>(sql: string, 
//     params: any[] = []): Promise<T[]> => {
//     return await pool.any(sql, params);
// };

// export const queryOne = async <T>(sql: string, 
//     params: any[] = []): Promise<T> => {
//     return await pool.one(sql, params);
// };


