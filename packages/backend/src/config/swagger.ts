import swaggerJsdoc from 'swagger-jsdoc';
import swaggerGenerator from '../utils/swagger-generator';

const options: swaggerJsdoc.Options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'PO105 API Documentation',
            version: '1.0.0',
            description:
                'API documentation for the AI-Powered Emergency Response Platform (PO105)',
            contact: {
                name: 'API Support',
                email: '<EMAIL>',
            },
            license: {
                name: 'Private',
            },
        },
        servers: [
            {
                url: 'http://localhost:4242',
                description: 'Development server',
            },
            {
                url: 'https://api.example.com',
                description: 'Production server',
            },
        ],
        components: {
            schemas: swaggerGenerator.generateSchemas(),
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
        security: [
            {
                bearerAuth: [],
            },
        ],
        tags: [
            {
                name: 'Auth',
                description: 'Authentication and authorization endpoints',
            },
            {
                name: 'Customer',
                description: 'Customer-related endpoints',
            },
            {
                name: 'SAP',
                description: 'SAP (Senior Authorised Person) related endpoints',
            },
            {
                name: 'Site',
                description: 'Site management endpoints',
            },
            {
                name: 'Substation',
                description: 'Substation management endpoints',
            },
            {
                name: 'Callout',
                description: 'Emergency callout endpoints',
            },
        ],
    },
    apis: [
        './src/controllers/*.ts', // Controller endpoints
        '../../../shared/src/interfaces/dto/**/*.ts', // Shared DTOs and interfaces
        '../../../shared/src/interfaces/responses/**/*.ts', // Shared responses
        '../../../shared/src/interfaces/requests/**/*.ts', // Shared requests
    ],
};

const swaggerSpec = swaggerJsdoc(options);

export default swaggerSpec;
