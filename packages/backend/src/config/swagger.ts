import swaggerJsdoc from 'swagger-jsdoc';
import swaggerGenerator from '../utils/swagger-generator';

const options: swaggerJsdoc.Options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'PO105 API Documentation',
            version: '1.0.0',
            description:
                'API documentation for the AI-Powered Emergency Response Platform (PO105)',
            contact: {
                name: 'API Support',
                email: '<EMAIL>',
            },
            license: {
                name: 'Private',
            },
        },
        servers: [
            {
                url: 'http://localhost:4242',
                description: 'Development server',
            },
            {
                url: 'https://api.example.com',
                description: 'Production server',
            },
        ],
        components: {
            schemas: swaggerGenerator.generateSchemas(),
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
        security: [
            {
                bearerAuth: [],
            },
        ],
        tags: [
            {
                name: 'Auth',
                description: 'Authentication and authorization endpoints',
            },
            {
                name: 'Customer',
                description: 'Customer-related endpoints',
            },
            {
                name: 'SAP',
                description: 'SAP (Senior Authorised Person) related endpoints',
            },
            {
                name: 'Site',
                description: 'Site management endpoints',
            },
            {
                name: 'Substation',
                description: 'Substation management endpoints',
            },
            {
                name: 'Callout',
                description: 'Emergency callout endpoints',
            },
        ],
    },
    apis: [
        './src/controllers/*.ts', // Controller endpoints
        '../../../shared/src/interfaces/dto/**/*.ts', // Shared DTOs and interfaces
        '../../../shared/src/interfaces/responses/**/*.ts', // Shared responses
        '../../../shared/src/interfaces/requests/**/*.ts', // Shared requests
    ],
};

const swaggerSpec = swaggerJsdoc(options);

// Post-process the spec to inject examples from discriminated unions
function injectDiscriminatedUnionExamples(spec: any) {
    const schemas = spec.components?.schemas;
    if (!schemas) return spec;

    // Find schemas with examples and inject them into relevant endpoints
    for (const [schemaName, schema] of Object.entries(schemas)) {
        if (schema && typeof schema === 'object' && (schema as any)._examples) {
            const examples = (schema as any)._examples;

            // For LoginResponse, inject examples into the login endpoint
            if (schemaName === 'LoginResponse') {
                injectLoginExamples(spec, examples);
            }

            // Clean up the temporary _examples property
            delete (schema as any)._examples;
        }
    }

    return spec;
}

function injectLoginExamples(spec: any, examples: Record<string, any>) {
    const loginPath = spec.paths?.['/auth/login']?.post;
    if (!loginPath) return;

    // Inject request examples
    const requestExamples: Record<string, any> = {};
    const responseExamples: Record<string, any> = {};

    for (const [key, example] of Object.entries(examples)) {
        if (key.endsWith('Request')) {
            requestExamples[key.replace('Request', '')] = example;
        } else {
            responseExamples[key] = example;
        }
    }

    // Add request examples
    if (Object.keys(requestExamples).length > 0) {
        if (!loginPath.requestBody) loginPath.requestBody = {};
        if (!loginPath.requestBody.content) loginPath.requestBody.content = {};
        if (!loginPath.requestBody.content['application/json']) {
            loginPath.requestBody.content['application/json'] = {};
        }
        loginPath.requestBody.content['application/json'].examples =
            requestExamples;
    }

    // Add response examples
    if (Object.keys(responseExamples).length > 0) {
        const successResponse = loginPath.responses?.['200'];
        if (successResponse?.content?.['application/json']) {
            successResponse.content['application/json'].examples =
                responseExamples;
        }
    }
}

// Apply post-processing
const enhancedSwaggerSpec = injectDiscriminatedUnionExamples(swaggerSpec);

export default enhancedSwaggerSpec;
