import dotenv from 'dotenv';													// Environment variables
dotenv.config({ 
    path: ['.env.local', '.env'] 
});																				// Initialize environment variables 

/* HELPER FUNCTIONS ======================================================== */

// string env variables
const getEnvVariable = (key: string): string => {
    const value = process.env[key];
    if (!value) {
        console.error(`${key} is not set`);
        throw new Error(`Environment variable ${key} is not set`);
    }
    return value;
};

// number env variables
const getNumberEnvVariable = (key: string): number => {
    const value = getEnvVariable(key);
    const numberValue = Number(value);
    if (isNaN(numberValue)) {
        console.error(`${key} is not a valid number`);
        throw new Error(`Environment variable ${key} is not a valid number`);
    }
    return numberValue;
};

// boolean env variables
const getBooleanEnvVariable = (key: string, defaultValue = false): boolean => {
    const value = process.env[key];
    if (!value) {
        return defaultValue;
    }
    return value.toLowerCase() === 'true';
};

/* SERVER PARAMETERS ======================================================= */

export const SERVER_PORT = getEnvVariable('SERVER_PORT');
export const API_BASE_URL = `${getEnvVariable('API_BASE_URL')}:${SERVER_PORT}`;


/* DEFAULT DATABASE PARAMETERS ============================================= */

export const DB_PORT = getEnvVariable('DB_PORT');
export const DB_NAME = getEnvVariable('DB_NAME');
export const DB_USER = getEnvVariable('DB_USER');
export const DB_PASSWORD = getEnvVariable('DB_PASSWORD');
export const DB_HOST = getEnvVariable('DB_HOST');
export const DB_MAX_POOL_SIZE = getNumberEnvVariable('DB_MAX_POOL_SIZE');

export const DB_CONNECTION_STRING = `postgresql://${DB_USER}:${DB_PASSWORD}@` +
                                    `${DB_HOST}:${DB_PORT}/${DB_NAME}`;

/* SUPABASE PARAMETERS ===================================================== */

export const SUPABASE_URL = getEnvVariable('SUPABASE_URL');
export const SUPABASE_SERVICE_KEY = getEnvVariable('SUPABASE_SERVICE_KEY');
export const URL_EXPIRY_SECONDS = getNumberEnvVariable('URL_EXPIRY_SECONDS');

/* DEPLOYMENT PARAMETERS =================================================== */

export const NODE_ENV = getEnvVariable('NODE_ENV');


