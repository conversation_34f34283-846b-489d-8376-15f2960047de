import { Router, Request, Response } from 'express';
import swaggerUi from 'swagger-ui-express';                                     // Swagger UI
import swaggerSpec from '../../config/swagger';                                    // Swagger specification

const router: Router = Router();

// Setup swagger routes
router.use('/', swaggerUi.serve);
router.get('/', swaggerUi.setup(swaggerSpec));

// Serve Swagger spec as JSON
router.get('/spec', (req: Request, res: Response) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

export default router; 