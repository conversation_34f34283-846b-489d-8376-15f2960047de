/** 
 * Auth-Related Endpoints
 * Primarily used for login and registration services
 */

/* IMPORTS ================================================================= */

import { Router } from 'express';
import authService from '../../services/AuthService';
import AuthController from '../../controllers/AuthController';

/* LOOSE-COUPLING / DI ===================================================== */

const router: Router = Router();
const authController = new AuthController(authService);

/* ENDPOINTS =============================================================== */


// Login endpoint
router.post('/login', authController.postLogin);


/* EXPORT ================================================================== */

export default router;

