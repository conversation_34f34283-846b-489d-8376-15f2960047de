// SERVER PUBLIC ROUTES

/* IMPORTS ================================================================= */
import { Router } from 'express';
import AuthRouter from './auth-router';
import SwaggerRouter from './swagger-router';

/* CONSOLIDATE ROUTES ====================================================== */
const router: Router = Router();

/* REGISTER ROUTES ========================================================= */
router.use('/auth', AuthRouter);
router.use('/api-docs', SwaggerRouter);

/* EXPORT ================================================================== */
export default router;

