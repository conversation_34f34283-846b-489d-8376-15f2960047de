import { Router } from 'express';
// import EmergencyController from '../../controllers/EmergencyController';
import { verifyToken } from '../../middlewares/auth/authorization';

const router: Router = Router();

router.use(verifyToken);

// // Emergency Declaration - Available to authenticated SiteManagers
// router.post('/declare', EmergencyController.declareEmergency);

// // Create Callout Request - Available to authenticated SiteManagers
// router.post('/callout-request', EmergencyController.createCalloutRequest);

// // Status Endpoints - Available to both SiteManagers and SAPs
// router.get('/callout-request/:calloutRequestId/status', EmergencyController.getCalloutRequestStatus);
// router.get('/work-order/:workOrderId/active-callout', EmergencyController.getActiveCallout);
// router.post('/callout-request/:calloutRequestId/create-callout', EmergencyController.createCallout);

// // SAP-specific endpoints
// router.get('/sap/callout-requests', EmergencyController.getSapCalloutRequests);
// router.get('/sap/active-callouts', EmergencyController.getSapActiveCallouts);
// router.get('/sap/active-callouts-locations', EmergencyController.getSapActiveCalloutsLocations);

export default router; 