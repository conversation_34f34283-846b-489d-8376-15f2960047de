// SERVER PROTECTED ROUTES

/* IMPORTS ================================================================= */
import { Router } from 'express';
import { verifyToken } from '@/middlewares/auth/authorization';
import EmergencyRouter from './emergency-router';

/* CONSOLIDATE ROUTES ====================================================== */
const router: Router = Router();

/* MIDDLEWARE ============================================================== */
// Only verify token globally
// role-based auth should be handled by individual routers
router.use(verifyToken);

/* REGISTER ROUTES ========================================================= */
router.use('/emergency', EmergencyRouter);

/* EXPORT =================================================================== */
export default router;

