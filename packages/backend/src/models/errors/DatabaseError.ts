/**
 * Database-related errors for the app's backend
 */

/* IMPORTS ================================================================ */


import { BaseError } from './BaseError';
import { ErrorDetails } from './types';

/* ERROR CLASS ============================================================ */


export class DatabaseError extends BaseError {
	protected static readonly __DEFAULTS = {
		ref: 'DB500',
		statusCode: 500,
		title: 'Database Error',
		message: 'An error occurred while accessing the database.',
	};

    constructor(
        {
            ref,
            statusCode,
            title,
            message,
            stackTrace,
            internalDescription,
            context
        }: Partial<ErrorDetails>,
		errorObject: any | null = null
	) {

		const retContext = context ?? {};

		if (errorObject && errorObject instanceof Error) {
			retContext['_raisedErrorMessage'] = errorObject.message;
			retContext['_raisedStackTrace'] = errorObject.stack;
		}

		const params = {
			ref, statusCode, title, message, 
			stackTrace, internalDescription, retContext
		};
		
        super({
            params: params,
            defaults: DatabaseError.__DEFAULTS,
        });
    }
}

