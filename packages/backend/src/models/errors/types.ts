
/**
 * TS interfaces for error params
 */

/* TYPES ================================================================== */

// Base shared properties
interface BaseErrorParams {
	ref?: string;                    // Reference code to identify error
	statusCode?: number;             // HTTP status code
	title?: string;                  // Error title
	message?: string;                // Raw emitted message
}
  
/* EXPOSED TYPES ========================================================== */

// Full error object description (used for parameter definition)
export interface ErrorDetails extends BaseErrorParams {
	stackTrace?: string;             // Stacktrace (internal use only)
	internalDescription?: string;    // Additional description (internal only)
	context?: Record<string, any>;   // Additional context (e.g., request data)
}

// Internal representation of the error (for server logs or DB entries)
export interface InternalErrorDetails extends Required<BaseErrorParams> {
	stackTrace?: string;
	internalDescription?: string;
	context?: Record<string, any>;
	timestamp?: Date;
	objectType?: string;
}

