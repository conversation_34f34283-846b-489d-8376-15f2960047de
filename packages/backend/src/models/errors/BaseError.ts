/**
 * BaseError
 *
 * The abstract parent class for all backend errors.
 * (Subclasses pass their own default values in the constructor).
 */

/* IMPORTS ================================================================ */

import {
    ErrorDetails,
    InternalErrorDetails,
} from './types';

/* ERROR CLASS ============================================================ */

export abstract class BaseError extends Error {
    protected readonly _error: ErrorDetails;
    protected readonly _timestamp: Date;

    // Fallback error details
    protected static readonly __DEFAULTS: ErrorDetails = {
		ref: 'U500',
		statusCode: 500,
		title: 'Unknown Error',
		message: 'An error has occurred.',
	}

    constructor({
        params = {},
        defaults = {}
    }: {
        params: ErrorDetails;
        defaults: Partial<ErrorDetails>;
    }) {
        const fallback = BaseError.__DEFAULTS;

        super(params.message ?? defaults.message ?? fallback.message);

        // Aggregate the error object (from passed params and defaults)
        this._error = {
            ref: params.ref ?? defaults.ref ?? fallback.ref,
            statusCode: params.statusCode ?? defaults.statusCode ?? 
            fallback.statusCode,
            title: params.title ?? defaults.title ?? fallback.title,
            message: params.message ?? defaults.message ?? fallback.message,
            stackTrace: params.stackTrace ?? defaults.stackTrace ?? this.stack,
            internalDescription: params.internalDescription ?? 
            defaults.internalDescription ?? fallback.internalDescription,
            context: params.context ?? defaults.context ?? fallback.context,
        };

        this._timestamp = new Date();

        // Fix the prototype chain
        Object.setPrototypeOf(this, new.target.prototype);
    }

    /**
     * Public-facing error information
     */
    get resJson(): Record<string, any> {

        const {
            ref, 
            title, 
            message, 
            statusCode
        } = this._error as Required<ErrorDetails>;

        return {
            ref, title, message,
            status: statusCode,
            formattedMessage: `(${ref}) - ${title}: ${message}`,
        };
    }

    /**
     * HTTP status code
     */
    get statusCode(): number {
        // non-null asserted as constructor guarantees non-null value;
        // we can force-unwrap here.
        return this._error.statusCode!;
    }

    /**
     * Internal error information for logs/auditing
     */
    internalAudit(): InternalErrorDetails {
        // Assert non-null since we fallbcak to defaults in the constructor
        const baseDetails = this._error as Required<ErrorDetails>;

        return {
            timestamp: this._timestamp,
            objectType: `${this.constructor.name}`,
            ...baseDetails
        }
    }

    /**
     * Log the error details if logging is enabled
     */
    log(): void {
        console.error(
            `${this.constructor.name}`,
            this._error,
            this.internalAudit()
        );
    }

    /**
     * Format a minimal error response (for HTTP APIs)
     */
    format(): string {
        const { 
            ref,
            title, 
            message 
        } = this._error;

        return `(${ref}) - ${title}: ${message}`;
    }
}

