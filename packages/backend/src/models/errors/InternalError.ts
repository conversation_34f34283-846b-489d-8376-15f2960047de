/**
 * Internal Server errors for the app's backend
 * 
 * These are errors that are not expected to happen. 
 * Stack Traces are logged and error objects are to be stored for analysis.
 */

/* IMPORTS ================================================================= */


import { BaseError } from './BaseError';
import { ErrorDetails } from './types';

/* ERROR CLASS ============================================================= */

export class InternalError extends BaseError {
	protected static readonly __DEFAULTS = {
		ref: 'I500',
		statusCode: 500,
		title: 'Internal Error',
		message: 'An internal error occurred.',
	};

	constructor(
		{
			ref,
			statusCode,
			title,
			message,
			stackTrace,
			internalDescription,
			context
		}: Partial<ErrorDetails>,
		errorObject: any | null = null
	) {

		const retContext = context ?? {};

		if (errorObject && errorObject instanceof Error) {
			retContext['_raisedErrorMessage'] = errorObject.message;
			retContext['_raisedStackTrace'] = errorObject.stack;
		}

		const params = {
			ref, statusCode, title, message, 
			stackTrace, internalDescription, retContext
		};
		
		super({
			params: params,
			defaults: InternalError.__DEFAULTS,
		});
	}
}

