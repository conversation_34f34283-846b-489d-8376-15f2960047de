/**
 * Not Found (404) error for the app's backend
 */

/* IMPORTS ================================================================ */


import { BaseError } from './BaseError';
import { ErrorDetails } from './types';

/* ERROR CLASS ============================================================ */


export class NotFoundError extends BaseError {
	protected static readonly __DEFAULTS = {
		ref: 'NF500',
		statusCode: 404,
		title: 'Not Found',
		message: 'The requested resource could not be found.',
		stackTrace: ''		// We don't need to keep track of stackTrace here
	};

    constructor(
        {
            ref,
            statusCode,
            title,
            message,
            stackTrace,
            internalDescription,
            context
        }: Partial<ErrorDetails>,
		errorObject: any | null = null
	) {

		const retContext = context ?? {};

		if (errorObject && errorObject instanceof Error) {
			retContext['_raisedErrorMessage'] = errorObject.message;
			retContext['_raisedStackTrace'] = errorObject.stack;
		}

		const params = {
			ref, statusCode, title, message, 
			stackTrace, internalDescription, retContext
		};
		
        super({
            params: params,
            defaults: NotFoundError.__DEFAULTS
        });
    }
}

