/**
 * Authentication/Authorization-related errors for the app's backend
 */

/* IMPORTS ================================================================ */

import { BaseError } from './BaseError';
import { ErrorDetails } from './types';

/* ERROR CLASS ============================================================ */

export class AuthError extends BaseError {
	protected static readonly __DEFAULTS = {
		ref: 'A500',
		statusCode: 401,
		title: 'Authentication Error',
		message: 'Access denied.',
		stackTrace: ''		// We don't need to keep track of stackTrace here
	};

    constructor(
        {
            ref,
            statusCode,
            title,
            message,
            stackTrace,
            internalDescription,
            context
        }: Partial<ErrorDetails>,
		errorObject: any | null = null
	) {

		const retContext = context ?? {};

		if (errorObject && errorObject instanceof Error) {
			retContext['_raisedErrorMessage'] = errorObject.message;
			retContext['_raisedStackTrace'] = errorObject.stack;
		}

		const params = {
			ref, statusCode, title, message, 
			stackTrace, internalDescription, retContext
		};

        super({
            params: params,
            defaults: AuthError.__DEFAULTS,
        });
    }
}
