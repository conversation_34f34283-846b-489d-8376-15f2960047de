/**
 * Authentication service
 **/

/* IMPORTS ================================================================= */

import { AuthError } from '../models/errors';
import ErrorCodes from '../constants/ErrorCodes';
import type { LoginRequest } from '@shared/interfaces/dto/requests';
import type { LoginResponse } from '@shared/interfaces/dto/responses';
import type { SapDto } from '@shared/interfaces/dto/models/sap-dto';
import type { SiteManagerProfileDto } from '@shared/interfaces/dto/models/site-manager-dto';
import { SapQueries, SiteManagerQueries } from '../database/queries';
import { UserId } from '@entities/po105_core/User';
import { supabase } from '../database/connection';
import { default as UserType } from '@entities/po105_core/UserTypeEnum';


/* SERVICE ================================================================= */

class AuthService {
    async login({ email, password }: LoginRequest): Promise<LoginResponse> {
        try {
            // -----------------------------------------------------------------
            // Authenticate with Supabase
            // -----------------------------------------------------------------
            const { data: authData, error: authError } =
                await supabase.auth.signInWithPassword({
                    email,
                    password,
                });

            if (authError) {
                throw new AuthError({
                    ...ErrorCodes.AUTH_LOGIN_FAILED,
                    internalDescription: authError.message,
                });
            }

            const { user: supabaseUser, session } = authData;
            if (!supabaseUser || !session) {
                // This case should ideally not be reached if authError is null
                // TODO: refactor this into an InternalServerError
                throw new AuthError({
                    ...ErrorCodes.AUTH_LOGIN_FAILED,
                    internalDescription:
                        'Supabase authentication successful but user or session data is missing.',
                });
            }

            const userType = supabaseUser.user_metadata?.user_type as UserType;
            const userId = supabaseUser.id as UserId;

            if (!userType) {
                // This case should ideally not be reached if authError is null
                // TODO: refactor this into an InternalServerError
                throw new AuthError({
                    ...ErrorCodes.AUTH_LOGIN_FAILED,
                    internalDescription:
                        'User type not found in Supabase user metadata.',
                });
            }

            // -----------------------------------------------------------------
            // Get the full user profile DTO from the query layer
            // -----------------------------------------------------------------
            if (userType === UserType.SAP) {
                const profile: SapDto | null =
                    await SapQueries.getFullProfile(userId);

                if (!profile) {
                    // TODO: refactor this into an InternalServerError,
                    // the existence of a Supabase user without a profile is a bug
                    throw new AuthError({
                        ...ErrorCodes.AUTH_PROFILE_NOT_FOUND,
                        internalDescription: `SAP profile not found for user ID: ${userId}`,
                    });
                }

                // Return SapLoginResponse
                return {
                    token: session.access_token,
                    profile,
                };
            } else if (userType === UserType.SITE_MANAGER) {
                const profile: SiteManagerProfileDto | null =
                    await SiteManagerQueries.getFullProfile(userId);

                if (!profile) {
                    // TODO: refactor this into an InternalServerError,
                    // the existence of a Supabase user without a profile is a bug
                    throw new AuthError({
                        ...ErrorCodes.AUTH_PROFILE_NOT_FOUND,
                        internalDescription: `Site Manager profile not found for user ID: ${userId}`,
                    });
                }

                // Return SiteManagerLoginResponse
                return {
                    token: session.access_token,
                    profile,
                };
            } else {
                // This should never happen
                // TODO: refactor this into an InternalServerError
                throw new AuthError({
                    ...ErrorCodes.AUTH_LOGIN_FAILED,
                    internalDescription: `Unsupported user type: ${userType}`,
                });
            }
        } catch (error) {
            if (error instanceof AuthError) throw error;
            // Ensure consistent error wrapping for unexpected errors
            const message =
                error instanceof Error
                    ? error.message
                    : 'Unknown error during login';
            throw new AuthError({
                ...ErrorCodes.AUTH_LOGIN_FAILED,
                internalDescription: `Login process failed: ${message}`,
            });
        }
    }
}

export default new AuthService();
