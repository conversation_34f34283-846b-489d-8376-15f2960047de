// import { logger } from '../utils/logger';
// import { EmergencyDeclarationResponse } from '@dto/responses/EmergencyDeclarationResponse';
// import { WorkOrderId } from '@entities/po105_job/WorkOrder';
// import { CalloutRequestId } from '@entities/po105_job/CalloutRequest';
// import { CalloutId } from '@entities/po105_job/Callout';
// import { SiteId } from '@entities/po105_management/Site';
// import { UserId } from '@entities/po105_core/User';
// import { NotFoundError, ValidationError, AuthError } from '../models/errors';
// import ErrorCodes from '../constants/ErrorCodes';
// import EmergencyQueries from '../database/queries/EmergencyQueries';
// import { db } from '../database/connection';
// import FaultTypeEnum from '@entities/po105_job/FaultTypeEnum';


// class EmergencyService {
//     private queries: typeof EmergencyQueries;

//     constructor() {
//         this.queries = EmergencyQueries;
//     }

//     /**
//      * Declare an emergency and create associated work order
//      * This is the entry point for the emergency flow:
//      * 1. Verify site access and get location
//      * 2. Create work order and fault triage
//      * 3. Find nearby SAPs
//      */
//     async declareEmergency(request: EmergencyDeclarationRequestWithUser): Promise<EmergencyDeclarationResponse> {
//         try {
//             // Start a transaction since we need to create multiple related records
//             return await db.transaction().execute(async (trx) => {
//                 // Get site location and verify access
//                 const site = await this.queries.getSiteLocation(request.site_id as SiteId, request.userId);
                
//                 if (!site) {
//                     throw new AuthError({
//                         ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
//                         internalDescription: `User ${request.userId} does not have access to site ${request.site_id}`
//                     });
//                 }

//                 // Parse location from GeoJSON
//                 const geoJson = typeof site.geog === 'string' ? JSON.parse(site.geog) : site.geog;
//                 if (!geoJson || !geoJson.coordinates) {
//                     throw new ValidationError({
//                         ...ErrorCodes.VALIDATION_ERROR,
//                         internalDescription: 'Invalid site location data'
//                     });
//                 }

//                 const siteLocation = {
//                     longitude: geoJson.coordinates[0],
//                     latitude: geoJson.coordinates[1]
//                 };

//                 // Create Work Order
//                 const workOrder = await this.queries.createWorkOrder({
//                     site_id: request.site_id as SiteId,
//                     site_manager_id: request.userId
//                 });

//                 if (!workOrder) {
//                     throw new Error('Failed to create work order');
//                 }

//                 // Create Fault Triage
//                 await this.queries.createFaultTriage({
//                     work_order_id: workOrder.id,
//                     fault_type: request.fault_type as FaultTypeEnum,
//                     severity: request.severity,
//                     priority: request.priority,
//                     description: request.description
//                 });

//                 // Find nearby SAPs
//                 const nearbySaps = await this.queries.findNearbySaps(siteLocation);

//                 return {
//                     work_order_id: workOrder.id,
//                     callout_request_id: '', // Empty since no callout request yet
//                     declaration_timestamp: new Date().toISOString(),
//                     status: 'PENDING',
//                     nearby_saps: nearbySaps
//                 };
//             });
//         } catch (error) {
//             logger.error('Error in declareEmergency:', error);
//             throw error;
//         }
//     }

//     /**
//      * Create a callout request for a specific SAP
//      * This is called after the site manager selects a SAP from the nearby list
//      */
//     async createCalloutRequest(params: CreateCalloutRequestParams): Promise<{ callout_request_id: CalloutRequestId; status: 'PENDING' }> {
//         try {
//             // Get work order details and verify access
//             const workOrder = await this.queries.getWorkOrderDetails(params.work_order_id, params.site_manager_id);
            
//             if (!workOrder) {
//                 throw new AuthError({
//                     ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
//                     internalDescription: `User ${params.site_manager_id} does not have access to work order ${params.work_order_id}`
//                 });
//             }

//             if (!workOrder.perm_location_id) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: `Work order ${params.work_order_id} has no location`
//                 });
//             }

//             // Calculate SAP's distance and time to site
//             const sapLocation = await this.queries.calculateSapDistanceAndTime(params.sap_id, workOrder.perm_location_id);

//             if (!sapLocation) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: `SAP ${params.sap_id} location not found`
//                 });
//             }

//             // Create Callout Request
//             const [calloutRequest] = await db
//                 .insertInto('po105_job.callout_request')
//                 .values({
//                     work_order_id: params.work_order_id,
//                     site_manager_id: params.site_manager_id,
//                     sap_id: params.sap_id,
//                     issuance_timestamp: new Date(),
//                     expires_after: '4 hours',
//                     estimated_distance_miles: sapLocation.distance_miles.toString(),
//                     estimated_time_mins: Math.round(sapLocation.distance_miles * 2), // Rough estimate: 30mph average speed
//                     created_at: new Date(),
//                     updated_at: new Date()
//                 })
//                 .returning(['id'])
//                 .execute();

//             if (!calloutRequest) {
//                 throw new Error('Failed to create callout request');
//             }

//             return {
//                 callout_request_id: calloutRequest.id,
//                 status: 'PENDING' as const
//             };
//         } catch (error) {
//             logger.error('Error in createCalloutRequest:', error);
//             throw error;
//         }
//     }

//     /**
//      * Check if a callout request exists and its status
//      * Used by both SAP and Site Manager to check request status
//      */
//     async getCalloutRequestStatus(calloutRequestId: CalloutRequestId, userId: UserId): Promise<'PENDING' | 'ACCEPTED' | 'REJECTED'> {
//         try {
//             const response = await this.queries.getCalloutRequestStatus(calloutRequestId, userId);

//             if (!response) {
//                 throw new AuthError({
//                     ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
//                     internalDescription: `User ${userId} does not have access to callout request ${calloutRequestId}`
//                 });
//             }

//             return response.response ? 'ACCEPTED' : 'REJECTED';
//         } catch (error) {
//             logger.error('Error in getCalloutRequestStatus:', error);
//             throw error;
//         }
//     }

//     /**
//      * Get active callout for a work order if it exists
//      * Used by Site Manager to check if work order has an active callout
//      */
//     async getActiveCallout(workOrderId: WorkOrderId, userId: UserId): Promise<CalloutId | null> {
//         try {
//             const callout = await this.queries.getActiveCallout(workOrderId, userId);
//             return callout?.id || null;
//         } catch (error) {
//             logger.error('Error in getActiveCallout:', error);
//             throw error;
//         }
//     }

//     /**
//      * Create a new callout for an accepted callout request
//      * Called when a SAP accepts a callout request
//      */
//     async createCallout(calloutRequestId: CalloutRequestId, userId: UserId): Promise<CalloutId> {
//         try {
//             // First verify the user has access to this callout request
//             const status = await this.queries.getCalloutRequestStatus(calloutRequestId, userId);

//             if (!status) {
//                 throw new AuthError({
//                     ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
//                     internalDescription: `User ${userId} does not have access to callout request ${calloutRequestId}`
//                 });
//             }

//             // First check if callout already exists
//             const existingCallout = await this.queries.getExistingCallout(calloutRequestId);

//             if (existingCallout) {
//                 return existingCallout.id;
//             }

//             // Verify the callout request is accepted
//             if (!status.response) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'Cannot create callout for unaccepted request'
//                 });
//             }

//             // Create the callout
//             const [callout] = await db
//                 .insertInto('po105_job.callout')
//                 .values({
//                     callout_request_id: calloutRequestId,
//                     callout_start_timestamp: new Date(),
//                     created_at: new Date(),
//                     updated_at: new Date()
//                 })
//                 .returning(['id'])
//                 .execute();

//             if (!callout) {
//                 throw new Error('Failed to create callout');
//             }

//             return callout.id;
//         } catch (error) {
//             logger.error('Error in createCallout:', error);
//             throw error;
//         }
//     }

//     async getSapCalloutRequests(sapId: UserId): Promise<WorkOrder[]> {
//         try {
//             const requests = await this.queries.getSapCalloutRequests(sapId);
//             return requests.map(req => ({
//                 id: req.id,
//                 site_name: req.site_name,
//                 fault_type: req.fault_type,
//                 declaration_timestamp: req.declaration_timestamp.toISOString(),
//                 description: req.fault_type, // Use fault type as description if none provided
//                 location: {
//                     address_line_1: req.address_line_1,
//                     city: req.city
//                 },
//                 type: 'CALLOUT_REQUEST',
//                 status: 'PENDING',
//                 estimated_distance_miles: req.estimated_distance_miles,
//                 estimated_time_mins: req.estimated_time_mins,
//                 expires_after: req.expires_after
//             }));
//         } catch (error) {
//             logger.error('Error in getSapCalloutRequests:', error);
//             throw error;
//         }
//     }

//     async getSapActiveCallouts(sapId: UserId): Promise<WorkOrder[]> {
//         try {
//             const callouts = await this.queries.getSapActiveCallouts(sapId);
//             return callouts.map(callout => ({
//                 id: callout.id,
//                 site_name: callout.site_name,
//                 fault_type: callout.fault_type,
//                 declaration_timestamp: callout.declaration_timestamp.toISOString(),
//                 description: callout.fault_type, // Use fault type as description if none provided
//                 location: {
//                     address_line_1: callout.address_line_1,
//                     city: callout.city
//                 },
//                 type: 'ACTIVE_CALLOUT',
//                 status: callout.status,
//                 callout_start_timestamp: callout.callout_start_timestamp.toISOString()
//             }));
//         } catch (error) {
//             logger.error('Error in getSapActiveCallouts:', error);
//             throw error;
//         }
//     }

//     async getSapActiveCalloutsLocations(sapId: UserId): Promise<ActiveCalloutLocation[]> {
//         try {
//             const callouts = await this.queries.getSapActiveCalloutsLocations(sapId);
//             return callouts.map(callout => ({
//                 id: callout.id,
//                 site_name: callout.site_name,
//                 fault_type: callout.fault_type,
//                 description: callout.fault_type, // Use fault type as description if none provided
//                 location: {
//                     latitude: callout.latitude,
//                     longitude: callout.longitude,
//                     address_line_1: callout.address_line_1,
//                     city: callout.city
//                 },
//                 status: callout.status,
//                 callout_start_timestamp: callout.callout_start_timestamp.toISOString()
//             }));
//         } catch (error) {
//             logger.error('Error in getSapActiveCalloutsLocations:', error);
//             throw error;
//         }
//     }
// }

// export default new EmergencyService(); 