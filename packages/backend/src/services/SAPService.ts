// /* IMPORTS ================================================================ */

// import { pool, sql } from '../database';                                        // Database connection
// import { BaseError, DatabaseError } from '../models/errors';                    // Error handling
// import ErrorCodes from '../assets/error_codes';                                 // Error codes
// import { logger as log } from '../utils';                                       // Logging utility
// import { SAP } from '../models/interfaces';                                     // Type definitions
// import { Point } from 'geojson';                                                // GeoJSON types

// /* INTERFACES ============================================================= */

// interface FindNearbySAPsOptions {
//     latitude: number;                                                           // Customer's latitude
//     longitude: number;                                                          // Customer's longitude
//     radiusMiles?: number;                                                       // Search radius (optional)
//     limit?: number;                                                             // Max results (optional)
// }

// // Response type for nearby SAPs search
// interface SAPSearchResult {
//     id: number;
//     email: string;
//     firstName: string;
//     lastName: string;
//     phoneNumber: string;
//     qualification: string;
//     availability: boolean;
//     ratePerMile: number;
//     ratePerHour: number;
//     distanceMiles: number;
//     registrationTimestamp: Date;
//     // Location information for map display
//     currentLocation?: {
//         coordinates: Point;                                                     // Current GPS location
//         accuracy?: number;                                                      // GPS accuracy in meters
//         timestamp: Date;                                                        // When coordinates were read
//     };
//     baseLocation?: {
//         coordinates: Point;                                                     // Base/permanent location
//         addressLine1: string;
//         addressLine2?: string;
//         city: string;
//         postcode?: string;
//         country: string;
//     };
//     partner: {
//         companyName: string;
//         companyRef: string;
//         contactEmail: string;
//         phoneNumber: string;
//     };
// }

// /* SERVICE METHODS ======================================================== */

// /**
//  * Find nearby SAPs based on location
//  * Uses PostGIS to calculate distances and find SAPs within radius
//  * 
//  * @param options - Search options including location and radius
//  * @returns Promise<SAPSearchResult[]> - Array of nearby SAPs with distances
//  */
// export const findNearbySAPs = async (options: FindNearbySAPsOptions): Promise<SAPSearchResult[]> => {
//     try {
//         const {
//             latitude,
//             longitude,
//             radiusMiles = 50,                                                  // Default 50 mile radius
//             limit = 10                                                         // Default 10 results
//         } = options;

//         // Create a PostGIS point from the provided coordinates
//         const point = `POINT(${longitude} ${latitude})`;

//         // Using raw SQL for PostGIS operations
//         // This is safe as all inputs are properly type-checked
//         const query = sql.unsafe`
//             WITH nearby_saps AS (
//                 -- First find SAPs within radius and calculate distances
//                 SELECT 
//                     s.user_id,
//                     s.availability,
//                     s.qualification,
//                     s.rate_per_mile,
//                     s.rate_per_hour,
//                     -- Current location details
//                     l.geom as current_location,
//                     l.gps_accuracy as current_location_accuracy,
//                     l.reading_timestamp as current_location_timestamp,
//                     -- Base location details
//                     pl.geom as base_location,
//                     pl.address_line_1 as base_address_line_1,
//                     pl.address_line_2 as base_address_line_2,
//                     pl.city as base_city,
//                     pl.postcode as base_postcode,
//                     pl.country as base_country,
//                     -- Calculate distance in miles using PostGIS
//                     ST_Distance(
//                         l.geom::geography,
//                         ST_SetSRID(ST_GeomFromText(${point}), 4326)::geography
//                     ) / 1609.34 as distance_miles
//                 FROM 
//                     mgmt_partner.sap s
//                     LEFT JOIN core.location l ON s.current_location_id = l.id
//                     LEFT JOIN mgmt.permanent_location pl ON s.perm_location_id = pl.id
//                 WHERE 
//                     s.availability = true
//                     AND s.deleted_at IS NULL
//                     -- Filter by radius (using current location if available, otherwise base location)
//                     AND (
//                         ST_DWithin(
//                             l.geom::geography,
//                             ST_SetSRID(ST_GeomFromText(${point}), 4326)::geography,
//                             ${radiusMiles} * 1609.34  -- Convert miles to meters
//                         )
//                         OR
//                         ST_DWithin(
//                             pl.geom::geography,
//                             ST_SetSRID(ST_GeomFromText(${point}), 4326)::geography,
//                             ${radiusMiles} * 1609.34  -- Convert miles to meters
//                         )
//                     )
//             )
//             -- Join with user_base to get user details
//             SELECT 
//                 ns.*,
//                 ub.email,
//                 ub.first_name,
//                 ub.last_name,
//                 ub.phone_number,
//                 ub.registration_timestamp,
//                 -- Include partner company details
//                 c.name as partner_company_name,
//                 c.internal_ref as partner_company_ref,
//                 c.contact_email as partner_company_email,
//                 c.phone_number as partner_company_phone
//             FROM 
//                 nearby_saps ns
//                 JOIN mgmt.user_base ub ON ns.user_id = ub.id
//                 LEFT JOIN mgmt_partner.sap s ON ns.user_id = s.user_id
//                 LEFT JOIN enrolment.partner p ON s.partner_id = p.id
//                 LEFT JOIN enrolment.company c ON p.company_id = c.id
//             WHERE 
//                 ub.deleted_at IS NULL
//             ORDER BY 
//                 ns.distance_miles ASC
//             LIMIT ${limit}
//         `;

//         const result = await pool.query(query);

//         return result.rows.map(row => ({
//             id: row.user_id,
//             email: row.email,
//             firstName: row.first_name,
//             lastName: row.last_name,
//             phoneNumber: row.phone_number,
//             qualification: row.qualification,
//             availability: row.availability,
//             ratePerMile: row.rate_per_mile,
//             ratePerHour: row.rate_per_hour,
//             distanceMiles: parseFloat(row.distance_miles),
//             registrationTimestamp: row.registration_timestamp,
//             // Map current location if available
//             currentLocation: row.current_location ? {
//                 coordinates: row.current_location,
//                 accuracy: row.current_location_accuracy,
//                 timestamp: row.current_location_timestamp
//             } : undefined,
//             // Map base location if available
//             baseLocation: row.base_location ? {
//                 coordinates: row.base_location,
//                 addressLine1: row.base_address_line_1,
//                 addressLine2: row.base_address_line_2,
//                 city: row.base_city,
//                 postcode: row.base_postcode,
//                 country: row.base_country
//             } : undefined,
//             partner: {
//                 companyName: row.partner_company_name,
//                 companyRef: row.partner_company_ref,
//                 contactEmail: row.partner_company_email,
//                 phoneNumber: row.partner_company_phone
//             }
//         }));

//     } catch (error) {
//         log.error('Error finding nearby SAPs:', error);
//         throw new DatabaseError({
//             ref: ErrorCodes.DATABASE_QUERY_ERROR.ref,
//             statusCode: 500,
//             title: 'Database Query Error',
//             message: 'Failed to find nearby SAPs',
//             internalDescription: error instanceof Error ? error.message : String(error),
//             context: { error }
//         });
//     }
// }; 