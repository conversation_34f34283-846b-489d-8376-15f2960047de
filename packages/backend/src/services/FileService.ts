/**
 * File service for handling file-related operations such as URL signing.
 */

/* IMPORTS ================================================================ */

import { supabase } from '@/database/connection';
import { logger } from '@/utils/logger';
import { URL_EXPIRY_SECONDS } from '@/config/env';

/* SERVICE ================================================================ */

class FileService {


    /**
     * Gets a signed URL for a file given its ID.
     * 
     * @param bucket_name - The name of the bucket to get a signed URL for
     * @param object_paths - The paths of the files to get a signed URL for
     * @returns A signed URL string or undefined if an error occurs
     */
    async getSignedFileUrl(
        bucket_name: string,
        object_path: string
    ): Promise<string | undefined> {
        try {
            const { data, error } = await supabase
                .storage
                .from(bucket_name)
                .createSignedUrl(
                    object_path,
                    URL_EXPIRY_SECONDS
                );

            if (error) {
                logger.error(
                    { 
                        bucketName: bucket_name, 
                        objectPath: object_path, 
                        err: error 
                    }, 
                    'Error creating signed URL in FileService');
                return undefined;
            }
            
            return data?.signedUrl;
        } catch (err) {
            logger.error(
                { 
                    bucketName: bucket_name, 
                    objectPath: object_path,
                    error: err 
                }, 
                'Exception during getSignedFileUrl in FileService');
            return undefined;
        }
    }

    /**
     * Gets a signed URL for a file given its ID.
     * 
     * @param bucket_name - The name of the bucket to get a signed URL for
     * @param object_paths - The paths of the files to get a signed URL for (owner_id/file_name)
     * @returns A signed URL string or undefined if an error occurs
     */
    async getSignedFileUrls(
        bucket_name: string,
        object_paths: string[]
    ): Promise<string[]> {
        try {
            const { data, error } = await supabase
                .storage
                .from(bucket_name)
                .createSignedUrls(
                    object_paths,
                    URL_EXPIRY_SECONDS
                );

            if (error) {
                logger.error(
                    { 
                        bucketName: bucket_name, 
                        objectPaths: object_paths, 
                        err: error 
                    }, 
                    'Error creating signed URL in FileService');
                return [];
            }
            
            return data?.map(signedFile => signedFile.signedUrl)    ;
        } catch (err) {
            logger.error(
                { 
                    bucketName: bucket_name, 
                    objectPaths: object_paths,
                    error: err 
                }, 
                'Exception during getSignedFileUrls in FileService');
            return [];
        }
    }

}

export default new FileService(); 