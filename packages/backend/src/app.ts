
/**
 * PO105 Backend Entry Point
 * 
 * This file is the main entry point for the backend application. It is 
 * responsible for initializing the Express application, configuring the 
 * middleware, and starting the HTTP and HTTPS servers.
 */

/* IMPORTS ================================================================= */

import express, { Request, Response, Express } from 'express';					// Middleware
import http from 'http';														// HTTP server creation
import https from 'https';                                                     	// HTTPS server creation
import cors from 'cors';														// CORS middleware
import fs from 'fs';                                                          	// File system for reading SSL certificates
import morgan from 'morgan';													// Request logger
import { logger } from './utils/logger';										// Internal server logging
// import session from 'express-session';										// Session middleware
import ErrorHandler from './middlewares/error-handler';							// Error-handling middleware
import { BaseError, NotFoundError } from './models/errors';						// Top-level error catching
import path from 'path';                                                        // Path manipulation
import PublicRouter from './routes/public';                         			// Auth router
import ProtectedRouter from './routes/protected';                               // API router

/* CONFIGURATION INITIALIZATION ============================================ */

// Config variables
import { 
	SERVER_PORT,
} from './config';
import ErrorCodes from './constants/ErrorCodes';

/* SERVER INITIALIZATION =================================================== */

// Initialize Express app
const app: Express = express();

// Create HTTP server
const httpServer = http.createServer(app);

// Create HTTPS server if SSL certificates exist
let httpsServer: https.Server | undefined;
const sslPath = path.join(process.cwd(), 'ssl');
if (fs.existsSync(path.join(sslPath, 'private.key')) && 
    fs.existsSync(path.join(sslPath, 'certificate.crt'))) {
    const privateKey = fs.readFileSync(path.join(sslPath, 'private.key'), 
												 'utf8');
    const certificate = fs.readFileSync(path.join(sslPath, 'certificate.crt'), 
												  'utf8');
    const credentials = { key: privateKey, cert: certificate };
    httpsServer = https.createServer(credentials, app);
}

/* MIDDLEWARE CONFIGURATION ================================================ */

app.use(cors({
	origin: '',
	credentials: true
}));																			// CORS middleware
app.use(morgan('dev'));															// Request logger
app.use(express.json());														// Parse JSON bodies
app.use(express.urlencoded({ extended: true }));								// Parse URL-encoded bodies

// DEPRECATED: in favor of Supabase storage
// Serve static files from storage directory
// const storageDir = path.join(process.cwd(), 'storage');
// app.use('/storage', express.static(storageDir));

/* SESSION CONFIGURATION =================================================== */

// DEPRECATED: no longer using sessions; maintained by Supabase
// app.use(session({
// 	secret: SESSION_SECRET,														// Session secret
// 	saveUninitialized: false,													// Not saving uninitialized sessions
// 	resave: true,																// Resave sessions
// 	rolling: true,																// Renew session on activity
// 	cookie: { 
//         maxAge: 60 * 24 * 60 * 60 * 1000,                                    // Session expires after 60 days
//         secure: isProductionEnv                                              // Use secure cookies in production
//     }
// }));

/* ROUTES ================================================================== */

app.use('/', PublicRouter);
app.use('/api', ProtectedRouter);

// Error handling middleware
app.use(ErrorHandler);

// Catch-all 404 handler - MUST be after all other routes
app.use((req: Request, res: Response) => {
	const errObj = new NotFoundError(ErrorCodes.NOT_FOUND_CATCH_ALL);
	res.status(404).json(errObj.resJson);
});

/* RUN SERVER ============================================================== */

// We resolve all app initialization promises prior to server start.
// Although top-level await is currently supported, it is unadvisable.
// Refer to https://stackoverflow.com/a/65257652/3551916.
// [DEPRECATED]: Removed all top-level promises; keeping this here in case we 
//               need to add promises back in the future.
Promise.all([]).then(async () => {
	// Logger + both database pools initialized successfully
	try {
        // Start HTTP server
        httpServer.listen(SERVER_PORT, () => {
            logger.info(`HTTP Server is running on port ${SERVER_PORT}...`);
        });

        // Start HTTPS server if available
        if (httpsServer) {
            httpsServer.listen(Number(SERVER_PORT) + 443, () => {
                logger.info(`HTTPS Server is running on
				port ${Number(SERVER_PORT) + 443}...`);
            });
        } else {
            logger.warn(
				'SSL certificates not found. HTTPS server not started.'
			);
        }
	} catch (err) {
		logger.error('Failed to start server:', err);
		process.exit(1);
	}
}).catch((err: BaseError)=>{
	// utils/logger.ts could potentially fail here, we use the native console
	// logger instead to report the error
	console.error(err.format());
}).catch((err) => {
	// fallback in case error cannot be cast into BaseError
	console.error(err);
});

// Export the app
export default app;

