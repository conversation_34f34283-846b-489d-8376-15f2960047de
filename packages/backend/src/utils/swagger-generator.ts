import { readdirSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';
import * as ts from 'typescript';
import { OpenAPIV3 } from 'openapi-types';

export class SwaggerGenerator {
    private sharedPath = join(__dirname, '../../../shared/src/interfaces');
    private samplesCache: Map<string, any> = new Map();

    generateSchemas(): OpenAPIV3.ComponentsObject['schemas'] {
        const schemas: OpenAPIV3.ComponentsObject['schemas'] = {};

        // Generate from DTOs (prioritize DTOs over entities for API documentation)
        this.processDirectory(join(this.sharedPath, 'dto'), schemas);

        // Generate from entities (only if not already defined in DTOs)
        this.processDirectory(join(this.sharedPath, 'entities'), schemas);

        // Post-process to resolve Pick utility types
        this.resolvePickTypes(schemas);

        // Enhance discriminated unions with examples from SAMPLES.md files
        this.enhanceDiscriminatedUnions(schemas);

        return schemas;
    }

    private processDirectory(
        dirPath: string,
        schemas: OpenAPIV3.ComponentsObject['schemas'],
    ) {
        try {
            const files = readdirSync(dirPath, { recursive: true });

            for (const file of files) {
                if (typeof file !== 'string' || !file.endsWith('.ts')) continue;

                const filePath = join(dirPath, file);
                const sourceFile = ts.createSourceFile(
                    file,
                    readFileSync(filePath, 'utf-8'),
                    ts.ScriptTarget.Latest,
                    true,
                );

                // Process each interface/type in the file
                ts.forEachChild(sourceFile, (node) => {
                    if (
                        ts.isInterfaceDeclaration(node) ||
                        ts.isTypeAliasDeclaration(node)
                    ) {
                        const schemaName = node.name.text;
                        // Only add if not already defined (DTOs take precedence over entities)
                        if (!schemas![schemaName]) {
                            const schema = this.convertToOpenAPISchema(
                                node,
                                sourceFile,
                            );
                            if (schema) {
                                schemas![schemaName] = schema;
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.warn(
                `Warning: Could not process directory ${dirPath}:`,
                error,
            );
        }
    }

    private convertToOpenAPISchema(
        node: ts.InterfaceDeclaration | ts.TypeAliasDeclaration,
        sourceFile: ts.SourceFile,
    ): OpenAPIV3.SchemaObject {
        const schema: OpenAPIV3.SchemaObject = {
            type: 'object',
            properties: {},
            required: [],
        };

        // Extract JSDoc description for the interface/type
        const description = this.extractJSDocDescription(node);
        if (description) {
            schema.description = description;
        }

        if (ts.isInterfaceDeclaration(node)) {
            // Handle inheritance (extends clause)
            if (node.heritageClauses) {
                for (const heritage of node.heritageClauses) {
                    if (heritage.token === ts.SyntaxKind.ExtendsKeyword) {
                        const extendedTypes = heritage.types.map((type) => {
                            const typeName = type.expression.getText();
                            return { $ref: `#/components/schemas/${typeName}` };
                        });

                        if (extendedTypes.length > 0) {
                            schema.allOf = [
                                ...extendedTypes,
                                {
                                    type: 'object',
                                    properties: {},
                                    required: [],
                                },
                            ];
                            // Move properties to the last allOf item
                            const lastItem = schema.allOf[
                                schema.allOf.length - 1
                            ] as OpenAPIV3.SchemaObject;
                            schema.properties = lastItem.properties;
                            schema.required = lastItem.required;
                        }
                    }
                }
            }

            node.members.forEach((member) => {
                if (ts.isPropertySignature(member) && member.name) {
                    const propName = member.name.getText();
                    const propSchema = this.getPropertyType(
                        member.type,
                        sourceFile,
                    );

                    // Extract JSDoc description for the property
                    const propDescription =
                        this.extractJSDocDescription(member);
                    if (propDescription) {
                        propSchema.description = propDescription;
                    }

                    schema.properties![propName] = propSchema;
                    if (!member.questionToken) {
                        schema.required!.push(propName);
                    }
                }
            });
        } else if (ts.isTypeAliasDeclaration(node)) {
            // Handle type aliases (union types, etc.)
            return this.handleTypeAlias(node, sourceFile);
        }

        return schema;
    }

    private getPropertyType(
        typeNode?: ts.TypeNode,
        sourceFile?: ts.SourceFile,
    ): OpenAPIV3.SchemaObject {
        if (!typeNode) return { type: 'string' };

        switch (typeNode.kind) {
            case ts.SyntaxKind.StringKeyword:
                return { type: 'string' };
            case ts.SyntaxKind.NumberKeyword:
                return { type: 'number' };
            case ts.SyntaxKind.BooleanKeyword:
                return { type: 'boolean' };
            case ts.SyntaxKind.ArrayType:
                return {
                    type: 'array',
                    items: this.getPropertyType(
                        (typeNode as ts.ArrayTypeNode).elementType,
                        sourceFile,
                    ),
                };
            case ts.SyntaxKind.TypeReference:
                const typeRef = typeNode as ts.TypeReferenceNode;
                const typeName = typeRef.typeName.getText();

                // Handle utility types like Pick, Omit, etc.
                if (
                    typeName === 'Pick' ||
                    typeName === 'Omit' ||
                    typeName === 'Partial'
                ) {
                    // For utility types, we'll just return a generic object type
                    // since we can't easily resolve them at compile time
                    return { type: 'object' };
                }

                // Handle Date type
                if (typeName === 'Date') {
                    return { type: 'string', format: 'date-time' };
                }

                return {
                    $ref: `#/components/schemas/${typeName}`,
                } as OpenAPIV3.SchemaObject;
            case ts.SyntaxKind.UnionType:
                return this.handleUnionType(
                    typeNode as ts.UnionTypeNode,
                    sourceFile,
                );
            case ts.SyntaxKind.LiteralType:
                return this.handleLiteralType(typeNode as ts.LiteralTypeNode);
            case ts.SyntaxKind.TypeLiteral:
                return this.handleTypeLiteral(
                    typeNode as ts.TypeLiteralNode,
                    sourceFile,
                );
            default:
                return { type: 'string' };
        }
    }

    /**
     * Extract JSDoc description from a TypeScript node
     */
    private extractJSDocDescription(node: ts.Node): string | undefined {
        const jsDocComments = ts.getJSDocCommentsAndTags(node);

        // Look for JSDoc comment text
        for (const comment of jsDocComments) {
            if (ts.isJSDoc(comment) && comment.comment) {
                if (typeof comment.comment === 'string') {
                    return comment.comment.trim();
                } else if (Array.isArray(comment.comment)) {
                    return comment.comment
                        .map((c) => c.text || '')
                        .join('')
                        .trim();
                }
            }
        }

        return undefined;
    }

    /**
     * Handle TypeScript union types (e.g., string | number)
     */
    private handleUnionType(
        unionType: ts.UnionTypeNode,
        sourceFile?: ts.SourceFile,
    ): OpenAPIV3.SchemaObject {
        const types = unionType.types.map((type) =>
            this.getPropertyType(type, sourceFile),
        );

        // Check if it's a simple enum-like union of string literals
        const allStringLiterals = unionType.types.every(
            (type) =>
                type.kind === ts.SyntaxKind.LiteralType &&
                (type as ts.LiteralTypeNode).literal.kind ===
                    ts.SyntaxKind.StringLiteral,
        );

        if (allStringLiterals) {
            return {
                type: 'string',
                enum: unionType.types.map(
                    (type) =>
                        (
                            (type as ts.LiteralTypeNode)
                                .literal as ts.StringLiteral
                        ).text,
                ),
            };
        }

        // For complex unions, use oneOf
        return {
            oneOf: types,
        };
    }

    /**
     * Handle TypeScript literal types (e.g., "PENDING", 42, true)
     */
    private handleLiteralType(
        literalType: ts.LiteralTypeNode,
    ): OpenAPIV3.SchemaObject {
        const literal = literalType.literal;

        if (ts.isStringLiteral(literal)) {
            return { type: 'string', enum: [literal.text] };
        } else if (ts.isNumericLiteral(literal)) {
            return { type: 'number', enum: [Number(literal.text)] };
        } else if (
            literal.kind === ts.SyntaxKind.TrueKeyword ||
            literal.kind === ts.SyntaxKind.FalseKeyword
        ) {
            return {
                type: 'boolean',
                enum: [literal.kind === ts.SyntaxKind.TrueKeyword],
            };
        }

        return { type: 'string' };
    }

    /**
     * Handle TypeScript type literals (inline object types)
     */
    private handleTypeLiteral(
        typeLiteral: ts.TypeLiteralNode,
        sourceFile?: ts.SourceFile,
    ): OpenAPIV3.SchemaObject {
        const schema: OpenAPIV3.SchemaObject = {
            type: 'object',
            properties: {},
            required: [],
        };

        typeLiteral.members.forEach((member) => {
            if (ts.isPropertySignature(member) && member.name) {
                const propName = member.name.getText();
                const propSchema = this.getPropertyType(
                    member.type,
                    sourceFile,
                );

                const propDescription = this.extractJSDocDescription(member);
                if (propDescription) {
                    propSchema.description = propDescription;
                }

                schema.properties![propName] = propSchema;
                if (!member.questionToken) {
                    schema.required!.push(propName);
                }
            }
        });

        return schema;
    }

    /**
     * Handle TypeScript type aliases
     */
    private handleTypeAlias(
        node: ts.TypeAliasDeclaration,
        sourceFile: ts.SourceFile,
    ): OpenAPIV3.SchemaObject {
        const description = this.extractJSDocDescription(node);

        // Check if this is a Pick/Omit utility type
        if (ts.isTypeReferenceNode(node.type)) {
            const typeName = node.type.typeName.getText();
            if (typeName === 'Pick' || typeName === 'Omit') {
                // For Pick/Omit types, try to resolve the properties if possible
                const typeArgs = node.type.typeArguments;
                if (typeArgs && typeArgs.length >= 2) {
                    const sourceType = typeArgs[0];

                    // If we can identify the source type, create a reference to it
                    if (ts.isTypeReferenceNode(sourceType)) {
                        const sourceTypeName = sourceType.typeName.getText();
                        return {
                            type: 'object',
                            description:
                                description ||
                                `${typeName} of ${sourceTypeName} properties`,
                            // We could potentially add a reference here, but for now keep it generic
                            // to avoid circular references
                        };
                    }
                }

                // Fallback to generic object with description
                return {
                    type: 'object',
                    description: description || `${typeName} utility type`,
                };
            }
        }

        const baseSchema = this.getPropertyType(node.type, sourceFile);

        if (description) {
            baseSchema.description = description;
        }

        return baseSchema;
    }

    /**
     * Post-process schemas to resolve Pick utility types with actual properties
     */
    private resolvePickTypes(schemas: OpenAPIV3.ComponentsObject['schemas']) {
        if (!schemas) return;

        // Create a map of known entity properties for common types
        const knownProperties: Record<
            string,
            Record<string, OpenAPIV3.SchemaObject>
        > = {
            User: {
                id: { type: 'string', description: 'User unique identifier' },
                email: {
                    type: 'string',
                    format: 'email',
                    description: 'User email address',
                },
                phone_number: {
                    type: 'string',
                    description: 'User phone number',
                },
                first_name: { type: 'string', description: 'User first name' },
                last_name: { type: 'string', description: 'User last name' },
                average_rating: {
                    type: 'number',
                    nullable: true,
                    description: 'User average rating',
                },
                total_ratings: {
                    type: 'number',
                    nullable: true,
                    description: 'Total number of ratings',
                },
                user_type: {
                    type: 'string',
                    enum: ['SAP', 'SITE_MANAGER'],
                    description: 'Type of user',
                },
            },
            Sap: {
                qualification: {
                    type: 'string',
                    nullable: true,
                    description: 'SAP qualification level',
                },
                availability: {
                    type: 'boolean',
                    description: 'Whether the SAP is currently available',
                },
                total_callouts: {
                    type: 'number',
                    nullable: true,
                    description: 'Total number of callouts completed',
                },
            },
            Company: {
                id: {
                    type: 'string',
                    description: 'Company unique identifier',
                },
                company_type: {
                    type: 'string',
                    description: 'Type of company',
                },
                name: { type: 'string', description: 'Company name' },
                contact_email: {
                    type: 'string',
                    format: 'email',
                    description: 'Company contact email',
                },
            },
            Site: {
                id: { type: 'string', description: 'Site unique identifier' },
                name: { type: 'string', description: 'Site name' },
                reference: {
                    type: 'string',
                    nullable: true,
                    description: 'Site reference number',
                },
            },
            Substation: {
                id: {
                    type: 'string',
                    description: 'Substation unique identifier',
                },
                reference: {
                    type: 'string',
                    nullable: true,
                    description: 'Substation reference',
                },
                name: { type: 'string', description: 'Substation name' },
                number: { type: 'string', description: 'Substation number' },
            },
            Customer: {
                id: {
                    type: 'string',
                    description: 'Customer unique identifier',
                },
            },
            File: {
                description: {
                    type: 'string',
                    nullable: true,
                    description: 'File description',
                },
                metadata: { type: 'object', description: 'File metadata' },
                tags: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'File tags',
                },
            },
            Media: {
                source: { type: 'string', description: 'Media source' },
                width: { type: 'number', description: 'Media width' },
                height: { type: 'number', description: 'Media height' },
            },
            Image: {
                mime_type: { type: 'string', description: 'Image MIME type' },
            },
            Audio: {
                mime_type: { type: 'string', description: 'Audio MIME type' },
            },
            Video: {
                mime_type: { type: 'string', description: 'Video MIME type' },
            },
            Document: {
                mime_type: {
                    type: 'string',
                    description: 'Document MIME type',
                },
            },
            LiveLocation: {
                geog: { type: 'object', description: 'Geographic coordinates' },
                gps_accuracy: {
                    type: 'number',
                    nullable: true,
                    description: 'GPS accuracy in meters',
                },
                reading_timestamp: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Timestamp of location reading',
                },
            },
            PermanentLocation: {
                geog: { type: 'object', description: 'Geographic coordinates' },
                w3w: {
                    type: 'string',
                    nullable: true,
                    description: 'What3Words address',
                },
                address_line_1: {
                    type: 'string',
                    nullable: true,
                    description: 'Address line 1',
                },
                address_line_2: {
                    type: 'string',
                    nullable: true,
                    description: 'Address line 2',
                },
                city: { type: 'string', nullable: true, description: 'City' },
                country: {
                    type: 'string',
                    nullable: true,
                    description: 'Country',
                },
                postcode: {
                    type: 'string',
                    nullable: true,
                    description: 'Postal code',
                },
            },
        };

        // Resolve specific Pick types by schema name
        const pickTypeResolvers: Record<
            string,
            { sourceType: string; keys: string[] }
        > = {
            BaseUserProfile: {
                sourceType: 'User',
                keys: [
                    'id',
                    'email',
                    'phone_number',
                    'first_name',
                    'last_name',
                    'average_rating',
                    'total_ratings',
                    'user_type',
                ],
            },
            BaseSap: {
                sourceType: 'Sap',
                keys: ['qualification', 'availability', 'total_callouts'],
            },
            BaseCompany: {
                sourceType: 'Company',
                keys: ['id', 'company_type', 'name', 'contact_email'],
            },
            BaseSite: {
                sourceType: 'Site',
                keys: ['id', 'name', 'reference'],
            },
            BaseSubstation: {
                sourceType: 'Substation',
                keys: ['id', 'reference', 'name', 'number'],
            },
            BaseCustomer: {
                sourceType: 'Customer',
                keys: ['id'],
            },
            BaseFile: {
                sourceType: 'File',
                keys: ['description', 'metadata', 'tags'],
            },
            BaseMedia: {
                sourceType: 'Media',
                keys: ['source', 'width', 'height'],
            },
            BaseImage: {
                sourceType: 'Image',
                keys: ['mime_type'],
            },
            BaseAudio: {
                sourceType: 'Audio',
                keys: ['mime_type'],
            },
            BaseVideo: {
                sourceType: 'Video',
                keys: ['mime_type'],
            },
            BaseDocument: {
                sourceType: 'Document',
                keys: ['mime_type'],
            },
            BaseLocation: {
                sourceType: 'LiveLocation',
                keys: ['geog', 'gps_accuracy', 'reading_timestamp'],
            },
            BasePermanentLocation: {
                sourceType: 'PermanentLocation',
                keys: [
                    'geog',
                    'w3w',
                    'address_line_1',
                    'address_line_2',
                    'city',
                    'country',
                    'postcode',
                ],
            },
        };

        // Resolve Pick types by schema name
        for (const [schemaName, resolver] of Object.entries(
            pickTypeResolvers,
        )) {
            if (schemas[schemaName]) {
                const sourceProperties = knownProperties[resolver.sourceType];
                if (sourceProperties) {
                    const pickedProperties: Record<
                        string,
                        OpenAPIV3.SchemaObject
                    > = {};
                    const requiredFields: string[] = [];

                    for (const key of resolver.keys) {
                        if (sourceProperties[key]) {
                            pickedProperties[key] = sourceProperties[key];
                            if (!sourceProperties[key].nullable) {
                                requiredFields.push(key);
                            }
                        }
                    }

                    // Replace the generic Pick schema with actual properties
                    const existingSchema = schemas[schemaName];
                    const existingDescription =
                        existingSchema && 'description' in existingSchema
                            ? existingSchema.description
                            : undefined;

                    schemas[schemaName] = {
                        type: 'object',
                        properties: pickedProperties,
                        required: requiredFields,
                        description:
                            existingDescription ||
                            `Pick of ${resolver.sourceType} properties`,
                    };
                }
            }
        }
    }

    /**
     * Enhance discriminated unions with examples from SAMPLES.md files
     */
    private enhanceDiscriminatedUnions(
        schemas: OpenAPIV3.ComponentsObject['schemas'],
    ) {
        if (!schemas) return;

        // Find discriminated unions (schemas with oneOf)
        for (const [schemaName, schema] of Object.entries(schemas)) {
            if (schema && typeof schema === 'object' && 'oneOf' in schema) {
                const examples = this.generateExamplesForDiscriminatedUnion(
                    schemaName,
                    schema,
                );
                if (examples && Object.keys(examples).length > 0) {
                    // Store examples in the schema for later use by the OpenAPI spec
                    (schema as any)._examples = examples;
                }
            }
        }
    }

    /**
     * Generate examples for a discriminated union by parsing SAMPLES.md files
     */
    private generateExamplesForDiscriminatedUnion(
        schemaName: string,
        schema: OpenAPIV3.SchemaObject,
    ): Record<string, OpenAPIV3.ExampleObject> | null {
        // Look for SAMPLES.md file in the same directory as the schema
        const samplesPath = this.findSamplesFile(schemaName);
        if (!samplesPath) return null;

        const samples = this.parseSamplesFile(samplesPath);
        if (!samples || samples.length === 0) return null;

        const examples: Record<string, OpenAPIV3.ExampleObject> = {};

        // Generate request examples based on response examples
        const requestExamples = this.generateRequestExamples(samples);

        // Add response examples
        samples.forEach((sample, index) => {
            const exampleKey = sample.name.toLowerCase().replace(/\s+/g, '');
            examples[exampleKey] = {
                summary: sample.name,
                description: sample.description,
                value: sample.data,
            };
        });

        // Add corresponding request examples
        requestExamples.forEach((requestExample, index) => {
            const exampleKey = `${requestExample.name.toLowerCase().replace(/\s+/g, '')}Request`;
            examples[exampleKey] = {
                summary: `${requestExample.name} Request`,
                description: `Login credentials for ${requestExample.name}`,
                value: requestExample.data,
            };
        });

        return examples;
    }

    /**
     * Find SAMPLES.md file for a given schema
     */
    private findSamplesFile(schemaName: string): string | null {
        // For LoginResponse, look in dto/responses/SAMPLES.md
        if (schemaName === 'LoginResponse') {
            const samplesPath = join(
                this.sharedPath,
                'dto/responses/SAMPLES.md',
            );
            if (existsSync(samplesPath)) {
                return samplesPath;
            }
        }

        // Generic search pattern for other schemas
        const possiblePaths = [
            join(this.sharedPath, `dto/responses/SAMPLES.md`),
            join(this.sharedPath, `dto/models/SAMPLES.md`),
            join(this.sharedPath, `dto/requests/SAMPLES.md`),
        ];

        for (const path of possiblePaths) {
            if (existsSync(path)) {
                return path;
            }
        }

        return null;
    }

    /**
     * Parse SAMPLES.md file and extract JSON examples
     */
    private parseSamplesFile(
        filePath: string,
    ): Array<{ name: string; description: string; data: any }> | null {
        try {
            const content = readFileSync(filePath, 'utf-8');
            const samples: Array<{
                name: string;
                description: string;
                data: any;
            }> = [];

            // Regular expression to match sections with JSON blocks
            const sectionRegex =
                /## `([^`]+)`\s*\n\n([^#]*?)```json\s*\n([\s\S]*?)\n```/g;

            let match;
            while ((match = sectionRegex.exec(content)) !== null) {
                const [, sectionName, description, jsonContent] = match;

                try {
                    // Clean up the JSON content (remove comments)
                    const cleanedJson = this.cleanJsonContent(jsonContent);
                    const parsedData = JSON.parse(cleanedJson);

                    samples.push({
                        name: sectionName,
                        description: description.trim(),
                        data: parsedData,
                    });
                } catch (jsonError) {
                    console.warn(
                        `Failed to parse JSON in section ${sectionName}:`,
                        jsonError,
                    );
                }
            }

            return samples.length > 0 ? samples : null;
        } catch (error) {
            console.warn(`Failed to read samples file ${filePath}:`, error);
            return null;
        }
    }

    /**
     * Clean JSON content by removing comments and fixing formatting
     */
    private cleanJsonContent(jsonContent: string): string {
        // Split into lines for more precise comment removal
        const lines = jsonContent.split('\n');
        const cleanedLines: string[] = [];

        for (let line of lines) {
            // Remove single-line comments but preserve strings that might contain //
            let inString = false;
            let escaped = false;
            let commentStart = -1;

            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                const nextChar = line[i + 1];

                if (escaped) {
                    escaped = false;
                    continue;
                }

                if (char === '\\' && inString) {
                    escaped = true;
                    continue;
                }

                if (char === '"' && !escaped) {
                    inString = !inString;
                    continue;
                }

                if (!inString && char === '/' && nextChar === '/') {
                    commentStart = i;
                    break;
                }
            }

            if (commentStart >= 0) {
                line = line.substring(0, commentStart);
            }

            // Trim whitespace and add if not empty
            line = line.trim();
            if (line) {
                cleanedLines.push(line);
            }
        }

        let result = cleanedLines.join(' ');

        // Remove multi-line comments
        result = result.replace(/\/\*[\s\S]*?\*\//g, '');

        // Remove trailing commas before closing brackets/braces
        result = result.replace(/,(\s*[}\]])/g, '$1');

        // Clean up extra whitespace
        result = result.replace(/\s+/g, ' ').trim();

        return result;
    }

    /**
     * Generate request examples based on response examples
     */
    private generateRequestExamples(
        samples: Array<{ name: string; description: string; data: any }>,
    ): Array<{ name: string; data: any }> {
        const requestExamples: Array<{ name: string; data: any }> = [];

        samples.forEach((sample) => {
            let requestData: any = {};

            // Extract email from the response to generate corresponding request
            if (sample.data?.profile?.email) {
                requestData.email = sample.data.profile.email;
                requestData.password = 'password123';
                requestData.device_uid = `device-${sample.name.toLowerCase().replace(/\s+/g, '-')}-${Math.random().toString(36).substr(2, 9)}`;
                requestData.ip_address = '*************';

                requestExamples.push({
                    name: sample.name
                        .replace('LoginResponse', '')
                        .replace('Response', '')
                        .trim(),
                    data: requestData,
                });
            }
        });

        return requestExamples;
    }
}

// Create and export the generator instance
const swaggerGenerator = new SwaggerGenerator();
export default swaggerGenerator;
