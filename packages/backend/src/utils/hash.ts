

/**
 * DEPRECATED: Using Supabase auth; no need to manually hash/verify passwords
 */

// import bcrypt from 'bcrypt';

// const hashPassword = async (password: string): Promise<string> => {
//   const saltRounds = 10;
//   return await bcrypt.hash(password, saltRounds);
// };

// const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
//   return await bcrypt.compare(password, hash);
// };

// export { hashPassword, verifyPassword };

