/* IMPORTS ================================================================= */

import { Request, Response, NextFunction } from 'express';
import AuthService from '@/services/AuthService';
import { LoginRequest } from '@shared/interfaces/dto/requests/login-request';
import { AuthError, ValidationError } from '@/models/errors';
import ErrorCodes from '@/constants/ErrorCodes';

/* CONTROLLER ============================================================== */

export default class AuthController {
    private authService: typeof AuthService;

    constructor(authService: typeof AuthService) {
        // service layer dependency injection
        this.authService = authService;
    }

    /**
     * @openapi
     * /auth/login:
     *   post:
     *     summary: Authenticate a user
     *     description: |
     *       Authenticates a user and returns a JWT token along with their profile information.
     *       The response structure varies based on the user type (SAP or Site Manager).
     *     tags: [Auth]
     *     requestBody:
     *       description: User login credentials
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/LoginRequest'
     *     responses:
     *       200:
     *         description: Login successful
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/LoginResponse'
     *       400:
     *         description: Validation error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ApiErrorResponse'
     *       401:
     *         description: Authentication failed
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ApiErrorResponse'
     */
    postLogin = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const loginRequest = req.body as LoginRequest;

            // Validate required fields
            if (!loginRequest.email || !loginRequest.password) {
                throw new ValidationError({
                    ...ErrorCodes.LOGIN_VALIDATION_FAILED,
                    internalDescription: 'Email and password are required',
                });
            }

            const response = await this.authService.login(loginRequest);
            res.json(response);
        } catch (error) {
            // Pass all errors to error handler
            next(
                error instanceof AuthError || error instanceof ValidationError
                    ? error
                    : new AuthError({
                          ...ErrorCodes.AUTH_LOGIN_FAILED,
                          internalDescription:
                              error instanceof Error
                                  ? error.message
                                  : 'Unknown error',
                      }),
            );
        }
    };
}
