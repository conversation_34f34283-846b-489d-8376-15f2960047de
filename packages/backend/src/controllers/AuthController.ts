/* IMPORTS ================================================================= */

import { Request, Response, NextFunction } from 'express';
import AuthService from '@/services/AuthService';
import { LoginRequest } from '@shared/interfaces/dto/requests/login-request';
import { AuthError, ValidationError } from '@/models/errors';
import ErrorCodes from '@/constants/ErrorCodes';

/* CONTROLLER ============================================================== */

export default class AuthController {
    private authService: typeof AuthService;

    constructor(authService: typeof AuthService) {
        // service layer dependency injection
        this.authService = authService;
    }

    /**
     * @openapi
     * /auth/login:
     *   post:
     *     summary: Authenticate a user
     *     description: |
     *       Authenticates a user and returns a JWT token along with their profile information.
     *       The response structure varies based on the user type (SAP or Site Manager).
     *     tags: [Auth]
     *     requestBody:
     *       description: User login credentials
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/LoginRequest'
     *           examples:
     *             sapLogin:
     *               summary: SAP Login
     *               description: Login credentials for a SAP user
     *               value:
     *                 email: "<EMAIL>"
     *                 password: "password123"
     *                 device_uid: "device-uuid-12345"
     *                 ip_address: "*************"
     *             siteManagerLogin:
     *               summary: Site Manager Login
     *               description: Login credentials for a Site Manager user
     *               value:
     *                 email: "<EMAIL>"
     *                 password: "password123"
     *                 device_uid: "device-uuid-67890"
     *                 ip_address: "*************"
     *     responses:
     *       200:
     *         description: Login successful
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/LoginResponse'
     *             examples:
     *               sapLoginResponse:
     *                 summary: SAP Login Response
     *                 description: Response when a SAP user logs in successfully
     *                 value:
     *                   token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
     *                   profile:
     *                     id: "sap-uuid-12345"
     *                     email: "<EMAIL>"
     *                     phone_number: "+************"
     *                     first_name: "Bob"
     *                     last_name: "The Builder"
     *                     average_rating: 4.9
     *                     total_ratings: 210
     *                     user_type: "SAP"
     *                     profile_image:
     *                       description: "SAP's profile picture"
     *                       metadata: {}
     *                       tags: ["profile", "sap"]
     *                       signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/sap_profile.jpg"
     *                       source: "CAMERA"
     *                       width: 600
     *                       height: 600
     *                       mime_type: "image/jpeg"
     *                     qualification: "HV Switching Operations"
     *                     availability: true
     *                     total_callouts: 45
     *                     partner:
     *                       company:
     *                         id: "company-uuid-325"
     *                         company_type: "PARTNER"
     *                         name: "Reliable HV Services Ltd."
     *                         contact_email: "<EMAIL>"
     *                         profile_image:
     *                           description: "Company Logo"
     *                           metadata: {}
     *                           tags: ["logo", "brand"]
     *                           signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/partner_logo.png"
     *                           source: "IMAGE_PICKER"
     *                           width: 300
     *                           height: 150
     *                           mime_type: "image/png"
     *                         perm_location:
     *                           geog:
     *                             type: "Point"
     *                             coordinates: [-0.140000, 51.520000]
     *                           w3w: "partner.office.location"
     *                           address_line_1: "1 Partner Road"
     *                           address_line_2: "Suite P"
     *                           city: "London"
     *                           country: "United Kingdom"
     *                           postcode: "WC1 1NE"
     *                         account_manager: null
     *                       sla_file:
     *                         description: "Partner SLA Document"
     *                         metadata: {}
     *                         tags: ["sla", "legal"]
     *                         signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/partner_sla.pdf"
     *                         mime_type: "application/pdf"
     *                     sla_file:
     *                       description: "Individual SAP SLA"
     *                       metadata: {}
     *                       tags: ["sla", "individual"]
     *                       signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/sap_sla.pdf"
     *                       mime_type: "application/pdf"
     *                     permanent_location:
     *                       geog:
     *                         type: "Point"
     *                         coordinates: [-0.125000, 51.515000]
     *                       w3w: "sap.home.location"
     *                       address_line_1: "123 SAP Street"
     *                       address_line_2: "Apt 4B"
     *                       city: "London"
     *                       country: "United Kingdom"
     *                       postcode: "SW1 1AA"
     *               siteManagerLoginResponse:
     *                 summary: Site Manager Login Response
     *                 description: Response when a Site Manager user logs in successfully
     *                 value:
     *                   token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************._x1gVAPFw8x0_qYjTzC6XqC_0gY9cVpY2lLsVvG8wBc"
     *                   profile:
     *                     id: "sitemgr-uuid-67890"
     *                     email: "<EMAIL>"
     *                     phone_number: "+************"
     *                     first_name: "Carol"
     *                     last_name: "Danvers"
     *                     average_rating: 4.2
     *                     total_ratings: 75
     *                     user_type: "SITE_MANAGER"
     *                     profile_image:
     *                       description: "Site Manager's profile picture"
     *                       metadata: {}
     *                       tags: ["profile", "sitemanager"]
     *                       signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/sitemgr_profile.jpg"
     *                       source: "CAMERA"
     *                       width: 400
     *                       height: 400
     *                       mime_type: "image/png"
     *                     company:
     *                       id: "company-uuid-635"
     *                       company_type: "CLIENT"
     *                       name: "MegaCorp Industries"
     *                       contact_email: "<EMAIL>"
     *                       profile_image:
     *                         description: "Company Logo"
     *                         metadata: {}
     *                         tags: ["logo", "corporate"]
     *                         signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/client_logo.png"
     *                         source: "IMAGE_PICKER"
     *                         width: 400
     *                         height: 200
     *                         mime_type: "image/png"
     *                       perm_location:
     *                         geog:
     *                           type: "Point"
     *                           coordinates: [-0.130000, 51.510000]
     *                         w3w: "client.office.location"
     *                         address_line_1: "456 Corporate Blvd"
     *                         address_line_2: "Floor 12"
     *                         city: "London"
     *                         country: "United Kingdom"
     *                         postcode: "EC1 2AB"
     *                       account_manager:
     *                         id: "user-uuid-am-789"
     *                         email: "<EMAIL>"
     *                         phone_number: "+************"
     *                         first_name: "Alice"
     *                         last_name: "Smith"
     *                         average_rating: 4.8
     *                         total_ratings: 50
     *                         user_type: "CUSTOMER_MANAGER"
     *                         profile_image:
     *                           description: "Account Manager's profile picture"
     *                           metadata: {}
     *                           tags: ["profile"]
     *                           signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/am_profile.jpg"
     *                           source: "IMAGE_PICKER"
     *                           width: 500
     *                           height: 500
     *                           mime_type: "image/jpeg"
     *                     sites:
     *                       - id: "site-uuid-456"
     *                         name: "North London Data Center"
     *                         reference: "NLDC-001"
     *                         cpl_file:
     *                           description: "Customer Premises Layout Plan"
     *                           metadata: {}
     *                           tags: ["CPL", "layout"]
     *                           signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/cpl.pdf"
     *                           mime_type: "application/pdf"
     *                         sld_file:
     *                           description: "Single Line Diagram"
     *                           metadata: {}
     *                           tags: ["SLD", "electrical"]
     *                           signedUrl: "https://example-bucket.s3.amazonaws.com/path/to/sld.pdf"
     *                           mime_type: "application/pdf"
     *                         substations:
     *                           - id: "substation-uuid-789"
     *                             reference: "SUB-001"
     *                             name: "Main Distribution"
     *                             number: "001"
     *       400:
     *         description: Validation error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ApiErrorResponse'
     *             examples:
     *               validationError:
     *                 summary: Validation Error
     *                 description: Error when required fields are missing
     *                 value:
     *                   ref: "LOGIN_VALIDATION_FAILED"
     *                   title: "Login Validation Failed"
     *                   message: "Email and password are required"
     *                   status: 400
     *       401:
     *         description: Authentication failed
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ApiErrorResponse'
     *             examples:
     *               authenticationError:
     *                 summary: Authentication Error
     *                 description: Error when credentials are invalid
     *                 value:
     *                   ref: "AUTH_LOGIN_FAILED"
     *                   title: "Authentication Failed"
     *                   message: "Invalid email or password"
     *                   status: 401
     */
    postLogin = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const loginRequest = req.body as LoginRequest;

            // Validate required fields
            if (!loginRequest.email || !loginRequest.password) {
                throw new ValidationError({
                    ...ErrorCodes.LOGIN_VALIDATION_FAILED,
                    internalDescription: 'Email and password are required',
                });
            }

            const response = await this.authService.login(loginRequest);
            res.json(response);
        } catch (error) {
            // Pass all errors to error handler
            next(
                error instanceof AuthError || error instanceof ValidationError
                    ? error
                    : new AuthError({
                          ...ErrorCodes.AUTH_LOGIN_FAILED,
                          internalDescription:
                              error instanceof Error
                                  ? error.message
                                  : 'Unknown error',
                      }),
            );
        }
    };
}
