// import { Request, Response, NextFunction } from 'express';
// import { EmergencyDeclarationRequest } from '@shared/interfaces/dto/requests/emergency-declaration-request';
// import { ValidationError } from '../models/errors';
// import ErrorCodes from '../constants/ErrorCodes';
// import EmergencyService from '../services/EmergencyService';
// import { CalloutRequestId } from '@shared/interfaces/entities/po105_job/CalloutRequest';
// import { WorkOrderId } from '@shared/interfaces/entities/po105_job/WorkOrder';
// import { UserId } from '@shared/interfaces/entities/po105_core/User';




// class EmergencyController {
//     static async declareEmergency(req: Request, res: Response, next: NextFunction) {
//         try {
//             const request = req.body as EmergencyDeclarationRequest;

//             // Validate required fields
//             if (!request.site_id || !request.description || !request.fault_type) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'site_id, description, and fault_type are required'
//                 });
//             }

//             // Validate severity and priority ranges
//             if (request.severity < 1 || request.severity > 5 || request.priority < 1 || request.priority > 5) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'severity and priority must be between 1 and 5'
//                 });
//             }

//             const response = await EmergencyService.declareEmergency({
//                 ...request,
//                 userId: req.user!.id as UserId
//             });
            
//             res.json(response);
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async createCalloutRequest(req: Request, res: Response, next: NextFunction) {
//         try {
//             const { work_order_id, sap_id } = req.body;

//             // Validate required fields
//             if (!work_order_id || !sap_id) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'work_order_id and sap_id are required'
//                 });
//             }

//             const response = await EmergencyService.createCalloutRequest({
//                 work_order_id: work_order_id as WorkOrderId,
//                 sap_id: sap_id as UserId,
//                 site_manager_id: req.user!.id as UserId
//             });
            
//             res.json(response);
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async getCalloutRequestStatus(req: Request, res: Response, next: NextFunction) {
//         try {
//             const { calloutRequestId } = req.params;

//             if (!calloutRequestId) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'calloutRequestId is required'
//                 });
//             }

//             const status = await EmergencyService.getCalloutRequestStatus(
//                 calloutRequestId as CalloutRequestId,
//                 req.user!.id as UserId
//             );
//             res.json({ status });
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async getActiveCallout(req: Request, res: Response, next: NextFunction) {
//         try {
//             const { workOrderId } = req.params;

//             if (!workOrderId) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'workOrderId is required'
//                 });
//             }

//             const calloutId = await EmergencyService.getActiveCallout(
//                 workOrderId as WorkOrderId,
//                 req.user!.id as UserId
//             );
//             res.json({ callout_id: calloutId });
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async createCallout(req: Request, res: Response, next: NextFunction) {
//         try {
//             const { calloutRequestId } = req.params;

//             if (!calloutRequestId) {
//                 throw new ValidationError({
//                     ...ErrorCodes.VALIDATION_ERROR,
//                     internalDescription: 'calloutRequestId is required'
//                 });
//             }

//             const calloutId = await EmergencyService.createCallout(
//                 calloutRequestId as CalloutRequestId,
//                 req.user!.id as UserId
//             );
//             res.json({ callout_id: calloutId });
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async getSapCalloutRequests(req: Request, res: Response, next: NextFunction) {
//         try {
//             const calloutRequests = await EmergencyService.getSapCalloutRequests(req.user!.id as UserId);
//             res.json({ callout_requests: calloutRequests });
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async getSapActiveCallouts(req: Request, res: Response, next: NextFunction) {
//         try {
//             const activeCallouts = await EmergencyService.getSapActiveCallouts(req.user!.id as UserId);
//             res.json({ active_callouts: activeCallouts });
//         } catch (error) {
//             next(error);
//         }
//     }

//     static async getSapActiveCalloutsLocations(req: Request, res: Response, next: NextFunction) {
//         try {
//             const activeCallouts = await EmergencyService.getSapActiveCalloutsLocations(req.user!.id as UserId);
//             res.json({ active_callouts: activeCallouts });
//         } catch (error) {
//             next(error);
//         }
//     }
// }

// export default EmergencyController; 