/**
 * Global Error-Handling Middleware
 *
 * All errors must be handled here. Failures in routers, controllers, 
 * business logic, database, etc., must delegate BaseError objects through 
 * `next()` all the way up the chain to this global handler.
 */

/* IMPORTS ================================================================ */
import { Request, Response, NextFunction } from 'express';
import { BaseError } from '../models/errors';
import { logger } from '../utils';
import ErrorCodes from '../constants/ErrorCodes';


/* MIDDLEWARE ============================================================= */

const ErrorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {

	if (err instanceof BaseError) {
		/* Structured error thrown ---------------------------------------- */
		err.log();
		// Response (formatted error object)
		res.status(err.statusCode).json(err.resJson);
	} else {
		/* Unknown error thrown ------------------------------------------- */

		// Log the full unknown error using Pino logger at error level
		logger.error({ err }, 'Unhandled error occurred'); // Pass the error object itself for better logging

		// Send generic internal server error response
		const internalError = ErrorCodes.INTERNAL_ERROR;
		// Ensure default values if ErrorCodes.INTERNAL_ERROR is somehow incomplete
		const statusCode = internalError?.statusCode ?? 500;
		const responseJson = {
			ref: internalError?.ref ?? 'I500',
			title: internalError?.title ?? 'Internal Server Error',
			message: internalError?.message ?? 'An internal error occurred.',
			status: statusCode
		};
		res.status(statusCode).json(responseJson);
	}

};

export default ErrorHandler;
