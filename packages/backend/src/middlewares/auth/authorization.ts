/* IMPORTS ================================================================ */

import { Request, Response, NextFunction } from 'express';
import { AuthError } from '@/models/errors';
import ErrorCodes from '@/constants/ErrorCodes';
import { supabase } from '@/database/connection';
import { UserId } from '@shared/interfaces/entities/po105_core/User';

/* INTERFACES ============================================================= */

export interface AuthorizationOptions {
    requiredRole?: string[];  // Array of allowed roles
}

interface SupabaseUser {
    id: UserId;
    email: string;
    role?: string;
}

declare global {
    namespace Express {
        interface Request {
            user?: SupabaseUser;
        }
    }
}

/* AUTHORIZATION ========================================================== */

export async function verifyToken(req: Request, res: Response, next: NextFunction) {
    try {
        const token = req.headers.authorization?.split(' ')[1]; // Bearer <token>
        
        if (!token) {
            throw new AuthError(ErrorCodes.AUTH_TOKEN_MISSING);
        }

        // Verify token with Supabase
        const { data: { user }, error } = await supabase.auth.getUser(token);
        
        if (error || !user) {
            throw new AuthError({
                ...ErrorCodes.AUTH_TOKEN_INVALID,
                internalDescription: error?.message || 'Invalid token'
            });
        }

        // Add user info to request
        req.user = {
            id: user.id as UserId,
            email: user.email!
        };
        
        next();
    } catch (err) {
        if (err instanceof AuthError) {
            next(err);
        } else {
            next(new AuthError({
                ...ErrorCodes.AUTH_TOKEN_INVALID,
                internalDescription: err instanceof Error ? err.message : 'Unknown error'
            }));
        }
    }
}

export function authorize(options: AuthorizationOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const user = req.user;

            if (!user) {
                throw new AuthError({
                    ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
                    internalDescription: 'User not authenticated'
                });
            }

            // Get user role from our database if needed
            if (!user.role && options.requiredRole) {
                const sapData = await supabase.from('po105_core.saps')
                    .select('id')
                    .eq('supabase_user_id', user.id)
                    .single();

                if (sapData.data) {
                    user.role = 'SAP';
                } else {
                    const siteManagerData = await supabase.from('po105_management.site_managers')
                        .select('id')
                        .eq('supabase_user_id', user.id)
                        .single();
                    
                    if (siteManagerData.data) {
                        user.role = 'SiteManager';
                    }
                }
            }

            // Role check if required
            if (options.requiredRole && (!user.role || !options.requiredRole.includes(user.role))) {
                throw new AuthError({
                    ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
                    internalDescription: `Required role: ${options.requiredRole.join(' or ')}, got: ${user.role || 'none'}`
                });
            }

            next();
        } catch (err) {
            if (err instanceof AuthError) {
                next(err);
            } else {
                next(new AuthError({
                    ...ErrorCodes.AUTH_INSUFFICIENT_PERMISSION,
                    internalDescription: err instanceof Error ? err.message : 'Unknown error'
                }));
            }
        }
    };
}

