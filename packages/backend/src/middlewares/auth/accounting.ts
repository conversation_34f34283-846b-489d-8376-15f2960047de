/* IMPORTS ================================================================ */

import { db } from '../../database/connection';
import AuthTypeEnum from '@entities/po105_auditing/AuthTypeEnum';
import AuthStatusEnum from '@entities/po105_auditing/AuthStatusEnum';

/* AUDIT LOGS ============================================================= */

/**
 * Logs a user action into the AuditLog table for tracking purposes.
 *
 */

export const logAuthAttempt = async (data: {
    attempt_timestamp: Date;
    ip_address: string;
    device_uid: string;
    auth_type: AuthTypeEnum;
    auth_status: AuthStatusEnum;
    auth_status_other?: string;
}) => {
    try {
        await db
            .insertInto('po105_auditing.auth_attempt_log')
            .values({
                attempt_timestamp: data.attempt_timestamp,
                ip_address: data.ip_address,
                device_uid: data.device_uid,
                auth_type: data.auth_type,
                auth_status: data.auth_status,
                auth_status_other: data.auth_status_other || null,
                created_at: new Date()
            })
            .execute();
    } catch (error: any) {
        // Log the error but don't throw - auth logging should not block the main flow
        console.error('Failed to log auth attempt:', error.message || error);
    }
};
