
<div align="center">
<h3 align="center">
<img src="https://i.imgur.com/l5v0Qp2.png" width="150" />

RESTful API Backend
</h3>


<h3>Developed with ❤️ and </h3>

<p align="center">

<img src="https://img.shields.io/badge/DigitalOcean-%230167ff.svg?logo=digitalOcean&logoColor=white" alt="DigitalOcean" />
<img src="https://img.shields.io/badge/Postgres-%23316192.svg?logo=postgresql&logoColor=white" alt="PostgreSQL" />
<img src="https://img.shields.io/badge/Node.js-6DA55F?logo=node.js&logoColor=white" alt="NodeJS" />
<img src="https://img.shields.io/badge/Express.js-%23404d59.svg?logo=express&logoColor=%2361DAFB" alt="Express" />
<img src="https://img.shields.io/badge/Stripe-5851DD?logo=stripe&logoColor=fff" alt="Stripe" />
<img src="https://img.shields.io/badge/React-%2320232a.svg?logo=react&logoColor=%2361DAFB" alt="React" />

</p>

<h5><em>In partnership with Midlands Power Networks, Ltd.</em></h5>

</div>


---

## 📒 Table of Contents
- [📒 Table of Contents](#-table-of-contents)
- [📍 Overview](#-overview)
- [⚙️ Features](#%EF%B8%8F-features)
- [📂 Project Structure](#project-structure)
- [🗺 Roadmap](#-roadmap)
- [🚀 Launch Plan](#-launch-plan)
- [📆 Timeline](#-timeline)
- [ℹ️ Additional Information](#-additional-information)
---


## 📍 Overview

**AI-Powered Emergency Response Platform**

 > This project is a software-driven emergency response platform aimed at improving service continuity and efficiency in the High Voltage (HV) sector. The focus is on optimising emergency response procedures by utilising AI to make the process of locating and dispatching a Senior Authorised Person (SAP) as efficient and streamlined as possible.

 > The concept broadly mimicks an Uber-like platform that allows HV customers experiencing an outage to easily request a SAP, ensuring prompt response and reducing financial losses. Additionally, the software is planned to incorporate equipment monitoring features, alerting customers to potential issues with transformers and switchgear to prevent outages before they occur. A built-in customer support system will assist with initial fault triaging, providing users with quick guidance and resources.

 > This project is a collaborative effort between Staffordshire University and Midlands Power Networks Ltd. through a Knowledge Transfer Partnership (KTP), and is informed by industry-specific requirements to elevate emergency response standards in the HV sector across the UK.

---

## ⚙️ Features

◦ **24/7 Emergency Response**: Efficient geolocation-based SAP dispatching, ensuring prompt attendance when emergencies arise.

◦ **AI-Powered Preliminary Triage**: Using state-of-the-art LLMs and RAG models, a virtual assistant will guide customers to better assess the nature of an emergency and give insights to the attending SAP.

◦ **HV/LV Suppliers Directory**: Partners and SAPs can access an up-to-date directory of HV/LV Suppliers, as well as their stock.

◦ **Equipment Replacement Recommendation System (possible patent)**: A recommendation system built to provide potentially cheaper combinations of equipment/replacement parts based on the aggragated suppliers' current stock.

◦ **Substation Health Monitoring using AI**: Using thermographic input in conjunction with other sensors' readings, an AI model can accurately determine the likelihood of equipment failure.

◦ **Proactive Maintenance & Analytics System**: Customers will be provided with a detailed, real-time analysis of their HV/LV equipment. Additionally, a notification system will be set to autonomously advise customers to request inspections to mitigate potential failures.


---


## 📂 Project Structure

:bangbang: ***The source and the project structure below should be kept private for security purposes.***

```cpp
.Server Repository
├─ .env                                                   // Environment Variables
├─ .gitignore
├─ README.md
├─ db                                                     // Database-related files (schema/etc.)
│  ├─ knex_migrations                                     // [Deprecated] - Knex.JS migrations/seeds
│  │  ├─ migrations
│  │  └─ seeds
│  └── migrations                                         // PostgreSQL versioned migrations
|     ├─ V***.sql
│     └─ V001_20241109172614__initial_schema.sql          // Initial production schema
├─ docs                                                   // Documentation files
│  ├─ PO105_ERD_*.pdf                                     // Latest production ERD
│  ├─ PO105_ERD_*.svg
│  ├─ archived                                            // Archived/Deprecated documentation files
│  ├─ work_plan.pdf                                       // Initial Gantt chart
│  └─ work_plan.png
├─ package-lock.json
├─ package.json                                           // NPM packages
├─ src                                                    // Server root directory
│  ├─ app.ts                                              // Server entry point
│  ├─ config                                              // Environment variables init.
│  │  ├─ env.ts
│  │  └─ index.ts
│  ├─ controllers                                         // Server's controllers 
│  │  ├─ *.ts
│  ├─ database                                            // Database connection
│  │  ├─ connection.ts                                    // Slonik connection init.
│  │  ├─ index.ts
|  |  └─ queries                                          // PostgreSQL queries (in TypeScript)
|  |     └─ *.ts
|  |
|  ├─ middlewares                                         // Express middleware layer
│  |  └─ auth                                             // Auth-related middleware
│  │     ├─ accounting.ts
│  │     ├─ authorization.ts
│  │     └─ index.ts
│  ├─ models                                              // Entity modelling
│  │  ├─ interfaces                                       // Typescript interfaces (for elementary entities)
│  │  │  ├─ AuditLog.ts
│  │  │  ├─ Charge.ts
│  │  │  ├─ Company.ts
│  │  │  ├─ Customer.ts
│  │  │  ├─ CustomerInvoice.ts
│  │  │  ├─ DIN.ts
│  │  │  ├─ Document.ts
│  │  │  ├─ Equipment.ts
│  │  │  ├─ FaultTriage.ts
│  │  │  ├─ File.ts
│  │  │  ├─ Image.ts
│  │  │  ├─ InstalledEquipment.ts
│  │  │  ├─ Location.ts
│  │  │  ├─ Media.ts
│  │  │  ├─ Message.ts
│  │  │  ├─ Partner.ts
│  │  │  ├─ PermanentLocation.ts
│  │  │  ├─ SAP.ts
│  │  │  ├─ SOP.ts
│  │  │  ├─ Site.ts
│  │  │  ├─ StockListing.ts
│  │  │  ├─ StockReservation.ts
│  │  │  ├─ Substation.ts
│  │  │  ├─ Supplier.ts
│  │  │  ├─ UserBase.ts
│  │  │  ├─ UserFeedback.ts
│  │  │  ├─ Video.ts
│  │  │  ├─ WorkOrder.ts
│  │  │  └─ index.ts
│  ├─ public                                              // Public-access assets
│  │  └─ assets
│  │     └─ images
|  ├─ routers                                             // Endpoint routing
|  |  └─ *.ts 
|  ├─ services                                            // Business logic (called by the controllers)
|  |  └─ *.ts 
│  └─ utils                                               // Utility scripts
│     ├─ ForeignKey.ts
│     ├─ hash.ts
│     ├─ index.ts
│     └─ logger.ts
└─ tsconfig.json                                          // Typescript configuration
```

---


## 🗺 Technical Roadmap

*Note: the following list is just a notepad for future technical improvements, not a roadmap for the platform's features.* 

<!-- #### QoS / UX
  - [ ] ℹ️ Task Q1: *System Name:* -->

#### Security
  - [X] ℹ️ Task S1: ~~*Database:* Implement and track multiple PostgreSQL users for each platform (i.e. mobile-app backend, website backend, etc.). Root user will only be used as a superuser for admin purposes.~~
  - [X] ℹ️ Task S2: ~~*Database:* Separate the designed database architecture into multiple schemas for improved separation of concerns.~~
  - [ ] ℹ️ Task S3: *Database:* Apply respective authorization levels to the newly created schemas (once we have assignable roles; i.e. when a dashboard user is created, etc.).
  - [ ] ℹ️ Task S4: *Server-Wide:* Implement an Express rate limiter as a middleware to prevent against DoS/DDoS attacks.

#### Functionality
  - [ ] ℹ️ Task F1: *Data:* Add a button in the sidebar menu to request `data erasure` (Refer to **Task L2**).

#### Management
  - [ ] ℹ️ Task M1: *Onboarding:* Implement a temporary portal to register Customers (+ their company, sites, substations, equipment, etc.) for testing.

#### Reliability
  - [ ] ℹ️ Task R1: *File System:* Ensure files' mimetype matches the encryption used.
  - [ ] ℹ️ Task R2: *Database:* Extend the database structure to contain historical data (e.g. when a SAP changes their address, we archive their old information rather than delete it (for support purposes)). Refer to **Task L1** for GDPR compliance.
  - [X] ℹ️ Task R3: *Design Pattern:* Implement a loose-coupling dependency injection pattern at the routing layer for easier testability.
  - [ ] ℹ️ Task R4: *Database Connection:* Investigate the metrics during the testing period to optimise the number of connection pools / number of connections per pool.

#### Privacy / Legality
  - [ ] ℹ️ Task L1: *UK GDPR:* Ensure adherence to *[Article 5(1).b](https://gdpr-info.eu/art-5-gdpr/)* "Purpose Limitation", *[Article 5(1).c](https://gdpr-info.eu/art-5-gdpr/)* "Data Minimisation", and *[Article 5(1).e](https://gdpr-info.eu/art-5-gdpr/)* "Storage Limitation", whereby soft-deleted rows and historical/archived data:
    1. Must be collected for a "specified, explicit and legitimate purpose"
    2. Must be "limited to what is necessary in relation to the purposes for which they are processed"
    3. Must be "kept ... no longer than is necessary for the purposes for which the personal data are processed" 
  - [ ] ℹ️ Task L2: *UK GDPR:* Ensure compliance with *[Article 16](https://gdpr-info.eu/art-16-gdpr/)* "Right to Rectification", *[Article 17](https://gdpr-info.eu/art-17-gdpr/)* "Right to Erasure" and *[Article 18](https://gdpr-info.eu/art-18-gdpr/)* "Right to Restriction of Processing", whereby data subjects may request:
    1. Data rectification for inaccurate personal data without undue delay
    2. Data erasure (a.k.a "right to be forgotten") for personal data that is no longer necessary to the purposes which they were collected
    3. Data processing restriction as opposed to erasure
  
  *Note:* It may be worth noting that *[Article 89.1-3](https://gdpr-info.eu/art-89-gdpr/)* allow extended data storage "for scientific or historical research purposes or statistical purposes". Moreover, *89.1* states that given sufficient pseudonymisations whereby the data subject is no longer identifiable, we may be allowed to indefinitely retain the data (make sure of this). 

---

## 🚀 Launch Plan

### Sprint 1: Base Application [*in-progress*]

  - [ ] *Backend*: Set up a secure database for user data and emergency records. Implement hashing and encryption protocols.
  - [ ] *Frontend*: Implement map markers, animations, and real-time emergency tracking.
  - [ ] *Testing*: Perform unit testing and load testing for scalability. Conduct user acceptance testing (UAT) for emergency requests.
  - [ ] *Deployment*: Deploy to a staging environment for testing and set up CI/CD pipelines.

### Sprint 2: Substation Health Monitoring

  - [ ] *Backend*: Integrate substation sensors for real-time data collection and implement API endpoints for health monitoring and alerts.
  - [ ] *AI Development*: Train predictive AI models using substation data for real-time failure predictions.
  - [ ] *Frontend*: Develop a dashboard for real-time substation health visualization and notifications.
  - [ ] *Testing*: Load test AI model accuracy and conduct UAT for new monitoring features.
  - [ ] *Deployment*: Deploy AI models and monitor performance with continuous updates.

### Sprint 3: Maintenance Features

  - [ ] *Backend*: Address client feature requests, optimize databases, and perform maintenance for system stability.
  - [ ] *Frontend*: Enhance UI/UX based on feedback and integrate requested features.
  - [ ] *Testing*: Conduct regression and final user acceptance testing for the entire platform.
  - [ ] *Deployment*: Final platform deployment with a long-term maintenance plan.


<!-- 
## 📆 Timeline

![Workplan](docs/gantt_chart.jpg) -->

## ℹ️ Additional Information
<!-- 
### Email Accounts

### Host Information -->

