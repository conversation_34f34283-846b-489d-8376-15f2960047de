{"entryFile": "src/app.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/controllers/**/*Controller.ts"], "spec": {"outputDirectory": "build", "specVersion": 3, "securityDefinitions": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "tags": [{"name": "Authentication", "description": "Authentication and user session management endpoints"}], "examples": {"loginRequest": {"email": "<EMAIL>", "password": "password123"}}}, "routes": {"routesDir": "build", "middleware": "express", "authenticationModule": "src/middlewares/auth/authorization.ts"}, "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "@shared/*": ["../shared/src/*"]}}}