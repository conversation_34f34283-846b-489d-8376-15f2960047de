{"dependencies": {"@nestjs/common": "^10.4.3", "@nestjs/core": "^10.4.3", "@nestjs/platform-express": "^10.4.3", "@nestjs/typeorm": "^10.0.2", "@supabase/supabase-js": "^2.49.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-session": "^1.18.0", "jsonwebtoken": "^9.0.2", "kysely": "^0.27.6", "module-alias": "^2.2.3", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.14.0", "pino": "^9.4.0", "process": "^0.11.10", "sql-template-strings": "^2.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsoa": "^6.6.0"}, "name": "@po105/backend", "version": "0.1.0", "private": true, "description": "Backend server for the PO105 Emergency Response Platform", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "pnpm run build && node --enable-source-maps -r source-map-support/register -r ./dist/alias.js dist/app.js", "clean": "rm -rf dist && rm -rf tsconfig.tsbuildinfo", "dev": "nodemon -r ts-node/register/transpile-only -r ./src/alias.ts src/app.ts", "serve": "node --enable-source-maps -r source-map-support/register -r ./dist/alias.js dist/app.js", "test": "jest", "lint": "echo \"No linting configured yet\" && exit 0", "generate-types": "npx kanel"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"@jest/types": "^29.6.3", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.18", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/geojson": "^7946.0.16", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/module-alias": "^2.0.4", "@types/morgan": "^1.9.9", "@types/node": "^22.15.18", "@types/passport": "^1.0.16", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.11.11", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcrypt": "^5.1.1", "copyfiles": "^2.4.1", "jest": "^29.7.0", "js-yaml": "^4.1.0", "kanel": "3.10.0", "kanel-kysely": "^0.6.1", "nodemon": "^3.1.7", "pino-pretty": "^11.2.2", "source-map-support": "^0.5.21", "ts-jest": "^29.3.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}}