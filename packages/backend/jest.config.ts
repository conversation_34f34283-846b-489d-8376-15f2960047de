

export default {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@shared/(.*)$': '<rootDir>/../shared/src/$1',
    '^@entities/(.*)$': '<rootDir>/../shared/src/interfaces/entities/$1'
  },
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/__tests__/**'
  ],
  testPathIgnorePatterns: ['/node_modules/', '/src/__tests__/setup.ts', '/dist/','.*\\.d\\.ts$' ],
};

