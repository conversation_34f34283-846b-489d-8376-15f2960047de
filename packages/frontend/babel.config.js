module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      '@babel/plugin-transform-runtime',
      [
        'module:react-native-dotenv',
        {
          moduleName: '@env',
          path: '.env',
          safe: false,
          allowUndefined: false,
          verbose: false
        },
      ],
      [
        'module-resolver',
        {
          root: ['./src'],
          extensions: ['.ios.js', '.android.js', '.js', '.ts', '.tsx', '.json'],
          alias: {
            '@': './src',
            '@components': './src/components',
            '@screens': './src/screens',
            '@hooks': './src/hooks',
            '@utils': './src/utils',
            '@styles': './src/styles',
            '@navigation': './src/navigation',
            '@services': './src/services',
            '@redux': './src/redux',
            '@contexts': './src/contexts',
            '@expo/vector-icons': '@expo/vector-icons',
            '@config/env': './src/config/env.ts',
            // '@env': '.env'
          }
        }
      ]
    ],
  };
};
