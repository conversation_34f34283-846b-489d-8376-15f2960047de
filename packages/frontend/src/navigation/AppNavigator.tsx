import React from 'react';
import {
    createStackNavigator,
    CardStyleInterpolators,
    StackNavigationOptions,
    TransitionPresets,
} from '@react-navigation/stack';
import { LandingScreen } from '../screens/LandingScreen';
import { LoginScreen } from '../screens/LoginScreen';
import { SignupScreen } from '../screens/SignupScreen';
import { MainScreen } from '../screens/MainScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import { ProfileScreen } from '../screens/ProfileScreen';

type AppStackParamList = {
    Landing: undefined;
    Login: undefined;
    Signup: undefined;
    Main: undefined;
    Settings: undefined;
    Profile: undefined;
};

const Stack = createStackNavigator<AppStackParamList>();

const screenOptions: StackNavigationOptions = {
    headerShown: false,
    cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS, // right to left
};

const AppNavigator: React.FC = () => {
    return (
        <Stack.Navigator
            screenOptions={screenOptions}
            initialRouteName='Landing'>
            {/* Onboarding and Login */}
            <Stack.Screen name='Landing' component={LandingScreen} />
            <Stack.Screen name='Login' component={LoginScreen} />
            <Stack.Screen name='Signup' component={SignupScreen} />

            {/* Main */}
            <Stack.Screen
                name='Main'
                component={MainScreen}
                options={{
                    cardStyleInterpolator:
                        CardStyleInterpolators.forFadeFromBottomAndroid,
                }}
            />

            {/* Settings */}
            <Stack.Screen
                name='Settings'
                component={SettingsScreen}
                options={{
                    cardStyleInterpolator:
                        CardStyleInterpolators.forHorizontalIOS,
                    gestureDirection: 'horizontal-inverted',
                }}
            />

            {/* Profile */}
            <Stack.Screen name='Profile' component={ProfileScreen} />
        </Stack.Navigator>
    );
};

export default AppNavigator;
