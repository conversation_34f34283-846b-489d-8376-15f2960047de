import { AppDispatch } from '@redux/store';
import { setCurrentUser } from '@redux/slices/userSlice';
import type { LoginResponse } from '@po105-app/shared/src/interfaces/dto/responses/login-response';
import type { LoginRequest } from '@po105-app/shared/src/interfaces/dto/requests/login-request';
import log from '@utils/log';
import * as SecureStore from 'expo-secure-store';
import { API_URL, SECURE_STORAGE_KEY_PREFIX, API_TIMEOUT } from '@/config/env';
import NetInfo from '@react-native-community/netinfo';
import {
    AppError,
    ErrorCodes,
    handleError,
    createHttpError,
} from '@utils/errors';
import { getDeviceId, getIpAddress } from '@utils/helpers';

/**
 * Authenticates a user by making a request to the backend API
 *
 * @param email User's email address
 * @param password User's password
 * @param dispatch Redux dispatch function
 * @returns The authenticated user data and token
 */
export const authenticateUser = async (
    email: string,
    password: string,
    dispatch: AppDispatch,
): Promise<LoginResponse> => {
    try {
        const netInfo = await NetInfo.fetch();

        if (!netInfo.isConnected) {
            throw new AppError(
                'No internet connection. Please check your network settings and try again.',
                ErrorCodes.NETWORK.NO_INTERNET,
            );
        }

        const loginRequest: LoginRequest = {
            email,
            password,
            device_uid: (await getDeviceId()) || '',
            ip_address: await getIpAddress(),
        };

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

        try {
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                },
                body: JSON.stringify(loginRequest),
                signal: controller.signal,
            };

            const response = await fetch(
                `${API_URL}/auth/login`,
                requestOptions,
            );
            clearTimeout(timeoutId);

            if (!response.ok) {
                let errorData;
                try {
                    errorData = await response.json();
                } catch {
                    errorData = {
                        message: `Server returned status ${response.status}`,
                    };
                }
                throw createHttpError(response.status, errorData);
            }

            const loginResponse = (await response.json()) as LoginResponse;

            // Store the token in secure storage
            await SecureStore.setItemAsync(
                `${SECURE_STORAGE_KEY_PREFIX}token`,
                loginResponse.token,
            );

            // Extract and store profile image URL if available
            if (
                loginResponse.profile.user_type === 'SAP' &&
                loginResponse.profile.profile_image?.signedUrl
            ) {
                console.log(
                    'Storing SAP profile image URL:',
                    loginResponse.profile.profile_image?.signedUrl,
                );
                await SecureStore.setItemAsync(
                    `${SECURE_STORAGE_KEY_PREFIX}profile_image`,
                    loginResponse.profile.profile_image?.signedUrl,
                );
            } else if (
                loginResponse.profile.user_type === 'SiteManager' &&
                'company' in loginResponse.profile
            ) {
                // If there's a company logo or profile image for site manager, store it
                const companyImage =
                    loginResponse.profile.company.profile_image?.signedUrl;
                console.log('SiteManager company image URL:', companyImage);
                if (companyImage) {
                    await SecureStore.setItemAsync(
                        `${SECURE_STORAGE_KEY_PREFIX}profile_image`,
                        companyImage,
                    );
                }
            } else {
                console.log('No profile image URL found in login response');
            }

            dispatch(setCurrentUser(loginResponse));
            return loginResponse;
        } catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                throw new AppError(
                    `Request timed out after ${API_TIMEOUT / 1000} seconds. Please check your connection and try again.`,
                    ErrorCodes.NETWORK.TIMEOUT,
                );
            }

            if (
                error instanceof TypeError &&
                error.message === 'Network request failed'
            ) {
                throw new AppError(
                    'Unable to connect to the server. Please check your connection and try again.',
                    ErrorCodes.NETWORK.REQUEST_FAILED,
                    error,
                );
            }

            throw error;
        }
    } catch (error) {
        throw handleError(error);
    }
};

/**
 * Retrieves the stored profile image URL
 *
 * @returns The profile image URL or null if not found
 */
export const getProfileImageUrl = async (): Promise<string | null> => {
    try {
        const imageUrl = await SecureStore.getItemAsync(
            `${SECURE_STORAGE_KEY_PREFIX}profile_image`,
        );
        console.log(
            'Retrieved profile image URL from secure storage:',
            imageUrl,
        );
        return imageUrl;
    } catch (error) {
        log.error('Error retrieving profile image URL', error);
        return null;
    }
};

/**
 * Fetches an image from a URL to ensure it's valid and accessible
 * For Supabase signed URLs, returns them directly without validation
 *
 * @param imageUrl The URL of the image to fetch
 * @returns The same URL if successful, or null if failed
 */
export const fetchImageFromUrl = async (
    imageUrl: string,
): Promise<string | null> => {
    if (!imageUrl || !imageUrl.startsWith('http')) {
        console.log('Invalid image URL format:', imageUrl);
        return null;
    }

    // For Supabase signed URLs, return them directly
    // These URLs are pre-authenticated and have a limited lifetime
    if (imageUrl.includes('supabase.co') || imageUrl.includes('supabase.in')) {
        console.log('Using Supabase signed URL directly:', imageUrl);
        return imageUrl;
    }

    // For non-Supabase URLs, do a basic validation
    try {
        console.log('Validating non-Supabase image URL:', imageUrl);
        const response = await fetch(imageUrl, { method: 'HEAD' });

        if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.startsWith('image/')) {
                return imageUrl;
            }
        }

        console.log('Image validation failed');
        return null;
    } catch (error) {
        console.error('Error validating image URL:', error);
        return null;
    }
};
