import { API_URL, SECURE_STORAGE_KEY_PREFIX } from '@/config/env';
import * as SecureStore from 'expo-secure-store';
import { createHttpError } from '@utils/errors';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';

export interface WorkOrderBase {
  id: string;
  site_name: string;
  fault_type: string;
  declaration_timestamp: string;
  description: string;
  location: {
    address_line_1: string;
    city: string;
  };
}

export interface CalloutRequest extends WorkOrderBase {
  type: 'CALLOUT_REQUEST';
  status: 'PENDING';
  estimated_distance_miles: string;
  estimated_time_mins: number;
  expires_after: string;
}

export interface ActiveCallout extends WorkOrderBase {
  type: 'ACTIVE_CALLOUT';
  status: 'IN_PROGRESS' | 'COMPLETED';
  callout_start_timestamp: string;
}

export interface ActiveCalloutLocation {
  id: string;
  site_name: string;
  fault_type: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address_line_1: string;
    city: string;
  };
  status: 'IN_PROGRESS' | 'COMPLETED';
  callout_start_timestamp: string;
}

export type WorkOrder = CalloutRequest | ActiveCallout;

export const fetchWorkOrders = async (isSap: boolean): Promise<WorkOrder[]> => {
  try {
    const token = await SecureStore.getItemAsync(`${SECURE_STORAGE_KEY_PREFIX}token`);
    
    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    // Different endpoints based on user type
    const endpoints = isSap ? [
      `${API_URL}/api/emergency/sap/callout-requests`,
      `${API_URL}/api/emergency/sap/active-callouts`
    ] : [
      `${API_URL}/api/emergency/site-manager/callout-requests`,
      `${API_URL}/api/emergency/site-manager/active-callouts`
    ];

    // Fetch both callout requests and active callouts
    const [requestsResponse, calloutsResponse] = await Promise.all([
      fetch(endpoints[0], {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }),
      fetch(endpoints[1], {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
    ]);

    if (!requestsResponse.ok || !calloutsResponse.ok) {
      throw createHttpError(requestsResponse.status || calloutsResponse.status, {
        message: 'Failed to fetch work orders'
      });
    }

    const [requestsData, calloutsData] = await Promise.all([
      requestsResponse.json(),
      calloutsResponse.json()
    ]);

    // Combine and sort by timestamp (most recent first)
    const allWorkOrders: WorkOrder[] = [
      ...(requestsData.callout_requests || []).map((req: any) => ({
        ...req,
        type: 'CALLOUT_REQUEST' as const
      })),
      ...(calloutsData.active_callouts || []).map((call: any) => ({
        ...call,
        type: 'ACTIVE_CALLOUT' as const
      }))
    ].sort((a, b) => 
      new Date(b.declaration_timestamp).getTime() - new Date(a.declaration_timestamp).getTime()
    );

    return allWorkOrders;
  } catch (error) {
    console.error('Error fetching work orders:', error);
    throw error;
  }
};

export const fetchSapActiveCallouts = async (): Promise<ActiveCalloutLocation[]> => {
  try {
    const token = await SecureStore.getItemAsync(`${SECURE_STORAGE_KEY_PREFIX}token`);
    
    if (!token) {
      throw new Error('No authentication token found. Please log in again.');
    }

    const response = await fetch(`${API_URL}/api/emergency/sap/active-callouts-locations`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw createHttpError(response.status, {
        message: 'Failed to fetch active callouts'
      });
    }

    const data = await response.json();
    return data.active_callouts || [];
  } catch (error) {
    console.error('Error fetching active callouts:', error);
    throw error;
  }
};