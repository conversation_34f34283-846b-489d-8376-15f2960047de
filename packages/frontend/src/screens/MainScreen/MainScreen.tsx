import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import {
    NavigationContainer,
    NavigationProp,
    useNavigation,
} from '@react-navigation/native';
import {
    createBottomTabNavigator,
    BottomTabScreenProps,
} from '@react-navigation/bottom-tabs';
import globalStyles from '../../styles/globalStyles';
import colors from '../../styles/colors';
import { WorkOrdersScreen } from '../WorkOrdersScreen';
import { MapScreen } from '../MapScreen';
import NotificationsScreen from '../NotificationsScreen/NotificationsScreen';
import TopNav from '../../components/menus/TopNav';
import { SafeAreaView } from 'react-native-safe-area-context';
import WorkOrdersIconInactive from '../../assets/images/vectors/icons/work-orders-inactive.svg';
import WorkOrdersIconActive from '../../assets/images/vectors/icons/work-orders-active.svg';
import MapIconInactive from '../../assets/images/vectors/icons/map-inactive.svg';
import MapIconActive from '../../assets/images/vectors/icons/map-active.svg';
import NotificationsIconInactive from '../../assets/images/vectors/icons/notifications-inactive.svg';
import NotificationsIconActive from '../../assets/images/vectors/icons/notifications-active.svg';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import {
    getProfileImageUrl,
    fetchImageFromUrl,
} from '../../services/loginService';
import { ProfileScreen } from '../ProfileScreen';
import log from '@/utils/log';
import * as SecureStore from 'expo-secure-store';
import { SECURE_STORAGE_KEY_PREFIX } from '@/config/env';

const Tab = createBottomTabNavigator();

type RootStackParamList = {
    Settings: undefined;
    Profile: undefined;
};

const MainScreen: React.FC = () => {
    const navigation = useNavigation<NavigationProp<RootStackParamList>>();
    const currentUser = useSelector(
        (state: RootState) => state.user.currentUser,
    );
    const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    // Determine initial route based on user type
    const initialRoute =
        currentUser &&
        'profile' in currentUser &&
        'type' in currentUser.profile &&
        currentUser.profile.type === 'Sap'
            ? 'Work Orders'
            : 'Map';

    // Fetch profile image when component mounts
    useEffect(() => {
        const fetchProfileImage = async () => {
            try {
                setIsLoading(true);
                const storedImageUrl = await getProfileImageUrl();

                if (storedImageUrl) {
                    const validatedUrl =
                        await fetchImageFromUrl(storedImageUrl);
                    if (validatedUrl) {
                        setProfileImageUrl(validatedUrl);
                    } else {
                        setProfileImageUrl(storedImageUrl);
                    }
                } else {
                    setProfileImageUrl(null);
                }
            } catch (error) {
                console.error('Error fetching profile image:', error);
                setProfileImageUrl(null);
            } finally {
                setIsLoading(false);
            }
        };

        fetchProfileImage();
    }, []);

    const navigateToSettings = () => {
        navigation.navigate('Settings');
    };

    const navigateToProfile = () => {
        navigation.navigate('Profile');
    };

    return (
        <SafeAreaView style={globalStyles.safeArea}>
            <View style={styles.container}>
                <TopNav
                    onProfilePress={navigateToProfile}
                    onSidebarPress={navigateToSettings}
                    profileImage={profileImageUrl || ''}
                />

                <Tab.Navigator
                    initialRouteName={initialRoute}
                    screenOptions={({ route }) => ({
                        tabBarIcon: ({ focused }) => {
                            let Icon;

                            if (route.name === 'Work Orders') {
                                Icon = focused
                                    ? WorkOrdersIconActive
                                    : WorkOrdersIconInactive;
                            } else if (route.name === 'Map') {
                                Icon = focused
                                    ? MapIconActive
                                    : MapIconInactive;
                            } else if (route.name === 'Notifications') {
                                Icon = focused
                                    ? NotificationsIconActive
                                    : NotificationsIconInactive;
                            }

                            if (!Icon) return null;

                            return (
                                <Icon
                                    width={24}
                                    height={24}
                                    style={{ marginBottom: 5 }}
                                />
                            );
                        },
                        tabBarLabel: route.name,
                        tabBarStyle: {
                            backgroundColor: colors.white,
                            borderTopWidth: 0,
                            elevation: 10,
                            height: Platform.OS === 'android' ? 80 : 60,
                            paddingBottom: Platform.OS === 'android' ? 20 : 0,
                        },
                        tabBarActiveTintColor: colors.neutral75,
                        tabBarInactiveTintColor: colors.neutral25,
                        tabBarLabelStyle: {
                            fontSize: 12,
                            fontWeight: 'bold',
                            marginBottom: -20,
                        },
                        tabBarItemStyle: {
                            justifyContent: 'center',
                            alignItems: 'center',
                            paddingVertical: 20,
                        },
                        headerShown: false,
                    })}>
                    <Tab.Screen
                        name='Work Orders'
                        component={WorkOrdersScreen}
                    />
                    <Tab.Screen name='Map' component={MapScreen} />
                    <Tab.Screen
                        name='Notifications'
                        component={NotificationsScreen}
                    />
                </Tab.Navigator>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.white,
    },
});

export default MainScreen;
