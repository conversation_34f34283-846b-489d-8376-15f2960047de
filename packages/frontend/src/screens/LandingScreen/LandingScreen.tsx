import { View, SafeAreaView, StyleSheet } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Image } from 'expo-image';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import Logo from '../../assets/images/vectors/logo-light.svg';
import Watermark from '../../assets/images/raster/watermark.png';
import globalStyles from '../../styles/globalStyles';
import PrimaryButton from '../../components/controls/PrimaryButton';
import OutlineButton from '../../components/controls/OutlineButton';

// Define the navigation type
type RootStackParamList = {
  Login: undefined;
  Signup: undefined;
};

const LandingScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const navigateToSignup = () => {
    navigation.navigate('Signup');
  };

  return (
    <SafeAreaView style={globalStyles.safeArea}>
    <KeyboardAwareScrollView
      contentContainerStyle={globalStyles.container}
      keyboardShouldPersistTaps='never'
      scrollEnabled={false}>
        
      <Image source={Watermark} style={styles.watermark} contentFit='contain' />

      <View style={{ height: '12.5%' }} />

      <View style={styles.logoContainer}>
        <Logo width='100%' height='100%' />
      </View>

      <View style={{ height: '10%' }} />

      <PrimaryButton
        onPress={navigateToLogin}
        text='Login'
        style={{ marginBottom: 10 }}
      />

      <OutlineButton onPress={navigateToSignup} text='Sign Up' />

      <View style={{ height: '12.5%' }} />
    </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  watermark: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  logoContainer: {
    width: '40%',
    height: '27.5%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default LandingScreen;
