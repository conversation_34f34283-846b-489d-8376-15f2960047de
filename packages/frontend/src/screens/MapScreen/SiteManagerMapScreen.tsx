import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  View, 
  StyleSheet, 
  Text, 
  ScrollView, 
  TouchableOpacity, 
  Platform,
  Alert,
} from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';
import { useSelector } from 'react-redux';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { RootState } from '../../redux/store';
import colors from '../../styles/colors';
import { SiteDto } from '@po105-app/shared/interfaces/dto/models/site-dto';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';
import { declareEmergency } from '../../services/emergencyService';
import EmergencyDeclarationSheet from './EmergencyDeclarationSheet';
import { EmergencyDeclarationResponse } from '@po105-app/shared/interfaces/dto/responses/EmergencyDeclarationResponse';
import { createCalloutRequest } from '../../services/calloutRequestService';
import { Image } from 'expo-image';
import styles, { CARD_WIDTH, CARD_SPACING } from './styles';

const SiteManagerMapScreen: React.FC = () => {
  const [selectedSite, setSelectedSite] = useState<SiteDto | null>(null);
  const [isDeclaringEmergency, setIsDeclaringEmergency] = useState(false);
  const [showEmergencySheet, setShowEmergencySheet] = useState(false);
  const [nearbySaps, setNearbySaps] = useState<EmergencyDeclarationResponse['nearby_saps']>([]);
  const [selectedSap, setSelectedSap] = useState<EmergencyDeclarationResponse['nearby_saps'][0] | null>(null);
  const [workOrderId, setWorkOrderId] = useState<string | null>(null);
  
  const mapRef = useRef<MapView>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const location = useSelector((state: RootState) => state.location.currentLocation);

  if (!currentUser || !('profile' in currentUser) || !('user_type' in currentUser.profile) || currentUser.profile.user_type !== 'SITE_MANAGER') {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Error loading user data. Please try again.
        </Text>
      </View>
    );
  }

  const sites = currentUser.profile.sites

  const getInitialRegion = useCallback((): Region => {
    // Prioritize user's current location
    if (location?.coords) {
      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };
    }

    // Fallback to London if no location available
    return {
      latitude: 51.5074,
      longitude: -0.1278,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    };
  }, [location]);

  const handleSitePress = useCallback((site: SiteDto, index: number) => {
    // Toggle selection if clicking on already selected site
    if (selectedSite?.id === site.id) {
      setSelectedSite(null);
      return;
    }

    setSelectedSite(site);
    // if (site.location) {
    //   mapRef.current?.animateToRegion({
    //     latitude: site.location.latitude,
    //     longitude: site.location.longitude,
    //     latitudeDelta: 0.01,
    //     longitudeDelta: 0.01,
    //   }, 1000);
    //   scrollViewRef.current?.scrollTo({
    //     x: index * (CARD_WIDTH + CARD_SPACING),
    //     animated: true,
    //   });
    // }
  }, [selectedSite]);

  const handleMarkerPress = useCallback((site: SiteDto) => {
    const index = sites.findIndex((s: SiteDto) => s.id === site.id);
    if (index !== -1) {
      handleSitePress(site, index);
    }
  }, [handleSitePress, sites]);

  const handleEmergencyPress = useCallback(() => {
    if (!selectedSite) return;
    
    // Trigger haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    setShowEmergencySheet(true);
  }, [selectedSite]);

  const handleEmergencySubmit = useCallback(async (data: {
    fault_type: 'COMPLETE_OUTAGE' | 'PARTIAL_OUTAGE' | 'HAZARD' | 'OTHER';
    severity: number;
    priority: number;
    description: string;
  }) => {
    if (!selectedSite) return;

    try {
      setIsDeclaringEmergency(true);
      
      const response = await declareEmergency({
        site_id: selectedSite.id,
        ...data
      });

      setShowEmergencySheet(false);
      setWorkOrderId(response.work_order_id);
      setNearbySaps(response.nearby_saps || []);

      // Zoom out to show all SAPs
      // if (response.nearby_saps?.length > 0 ) {
      //   const bounds = response.nearby_saps.reduce(
      //     (acc, sap) => {
      //       return {
      //         minLat: Math.min(acc.minLat, sap.location.latitude),
      //         maxLat: Math.max(acc.maxLat, sap.location.latitude),
      //         minLng: Math.min(acc.minLng, sap.location.longitude),
      //         maxLng: Math.max(acc.maxLng, sap.location.longitude),
      //       };
      //     },
      //     {
      //       minLat: selectedSite.location.latitude,
      //       maxLat: selectedSite.location.latitude,
      //       minLng: selectedSite.location.longitude,
      //       maxLng: selectedSite.location.longitude,
      //     }
      //   );

      //   const center = {
      //     latitude: (bounds.minLat + bounds.maxLat) / 2,
      //     longitude: (bounds.minLng + bounds.maxLng) / 2,
      //   };

      //   const span = {
      //     latitudeDelta: (bounds.maxLat - bounds.minLat) * 1.5,
      //     longitudeDelta: (bounds.maxLng - bounds.minLng) * 1.5,
      //   };

      //   mapRef.current?.animateToRegion({
      //     ...center,
      //     ...span,
      //   }, 1000);
      // }

      Alert.alert(
        'Emergency Declared',
        'Please select a Senior Authorised Person from the map to attend to your emergency.',
        [{ text: 'OK' }]
      );
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to declare emergency. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsDeclaringEmergency(false);
    }
  }, [selectedSite]);

  const handleSapPress = useCallback((sap: EmergencyDeclarationResponse['nearby_saps'][0]) => {
    setSelectedSap(sap);
    mapRef.current?.animateToRegion({
      latitude: sap.location.latitude,
      longitude: sap.location.longitude,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    }, 1000);
  }, []);

  const handleRequestSap = useCallback(async () => {
    if (!selectedSap || !workOrderId) return;

    try {
      const response = await createCalloutRequest({
        work_order_id: workOrderId,
        sap_id: selectedSap.id,
      });

      Alert.alert(
        'Request Sent',
        `Request sent to ${selectedSap.first_name} ${selectedSap.last_name}. They will respond shortly.`,
        [{ text: 'OK' }]
      );

      // Clear SAP selection and work order
      setSelectedSap(null);
      setWorkOrderId(null);
      setNearbySaps([]);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to send request. Please try again.',
        [{ text: 'OK' }]
      );
    }
  }, [selectedSap, workOrderId]);

  // Animate to user location when it becomes available
  useEffect(() => {
    if (location?.coords && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      }, 1000);
    }
  }, [location]);

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        initialRegion={getInitialRegion()}
        showsUserLocation
        showsMyLocationButton
        provider={Platform.select({
          ios: undefined, // Use Apple Maps on iOS
          android: 'google' // Use Google Maps on Android
        })}
      >
        {/* Show sites */}
        {/* {sites.map((site: SiteDto) => (
          site&& (
            <Marker
              key={site.id}
              coordinate={{
                latitude: site.location.latitude,
                longitude: site.location.longitude,
              }}
              onPress={() => handleSitePress(site, sites.indexOf(site))}
            >
              <View style={[
                styles.siteMarker,
                selectedSite?.id === site.id && styles.selectedSiteMarker
              ]}>
                <MaterialCommunityIcons
                  name="lightning-bolt"
                  size={24}
                  color={selectedSite?.id === site.id ? colors.white : colors.primaryMain}
                />
              </View>
            </Marker>
          )
        ))} */}

        {/* Show nearby SAPs during emergency */}
        {nearbySaps.map(sap => (
          <Marker
            key={sap.id}
            coordinate={sap.location}
            onPress={() => handleSapPress(sap)}
          >
            <View style={[
              styles.sapMarker,
              selectedSap?.id === sap.id && styles.selectedSapMarker
            ]}>
              {sap.profile_picture_url ? (
                <Image
                  source={{ uri: sap.profile_picture_url }}
                  style={styles.sapProfilePic}
                />
              ) : (
                <MaterialCommunityIcons
                  name="account"
                  size={24}
                  color={selectedSap?.id === sap.id ? colors.white : colors.primaryMain}
                />
              )}
            </View>
          </Marker>
        ))}
      </MapView>

      {/* Show error message if no sites with valid locations */}
      {sites.length === 0 && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            No sites with valid locations found. Please contact support.
          </Text>
        </View>
      )}

      {/* Horizontal Site/SAP Selector */}
      <View style={styles.siteListContainer}>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          snapToInterval={CARD_WIDTH + CARD_SPACING}
          decelerationRate="fast"
          contentContainerStyle={styles.scrollViewContent}
        >
          {nearbySaps.length > 0 ? (
            // Show SAP cards when available
            nearbySaps.map((sap) => (
              <TouchableOpacity
                key={sap.id}
                activeOpacity={0.8}
                onPress={() => handleSapPress(sap)}
              >
                <LinearGradient
                  colors={selectedSap?.id === sap.id 
                    ? [colors.primaryMain, '#1468B7']
                    : [colors.white, colors.neutral5]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={[
                    styles.sapCard,
                    selectedSap?.id === sap.id && styles.selectedSapCard
                  ]}
                >
                  <View style={styles.sapCardHeader}>
                    {sap.profile_picture_url ? (
                      <Image
                        source={{ uri: sap.profile_picture_url }}
                        style={styles.sapCardProfilePic}
                        placeholder={colors.neutral5}
                        contentFit="cover"
                        transition={200}
                      />
                    ) : (
                      <MaterialCommunityIcons
                        name="account"
                        size={32}
                        color={selectedSap?.id === sap.id ? colors.white : colors.primaryMain}
                      />
                    )}
                    <View style={styles.sapCardInfo}>
                      <Text 
                        style={[
                          styles.sapName,
                          selectedSap?.id === sap.id && styles.selectedText
                        ]}
                      >
                        {sap.first_name} {sap.last_name}
                      </Text>
                      <View style={styles.sapRating}>
                        <MaterialCommunityIcons
                          name="star"
                          size={16}
                          color={selectedSap?.id === sap.id ? colors.white : colors.mainYellow}
                        />
                        <Text
                          style={[
                            styles.sapRatingText,
                            selectedSap?.id === sap.id && styles.selectedText
                          ]}
                        >
                          {sap.rating.toFixed(1)}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.sapCardDetails}>
                    <View style={styles.sapDetailItem}>
                      <MaterialCommunityIcons
                        name="map-marker-distance"
                        size={16}
                        color={selectedSap?.id === sap.id ? colors.white : colors.neutral50}
                      />
                      <Text
                        style={[
                          styles.sapDetailText,
                          selectedSap?.id === sap.id && styles.selectedText
                        ]}
                      >
                        {sap.distance_miles.toFixed(2)} miles away
                      </Text>
                    </View>
                    <View style={styles.sapDetailItem}>
                      <MaterialCommunityIcons
                        name="clock-outline"
                        size={16}
                        color={selectedSap?.id === sap.id ? colors.white : colors.neutral50}
                      />
                      <Text
                        style={[
                          styles.sapDetailText,
                          selectedSap?.id === sap.id && styles.selectedText
                        ]}
                      >
                        {sap.estimated_arrival_mins} mins ETA
                      </Text>
                    </View>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            ))
          ) : (
            // Show site cards by default
            sites.map((site: SiteDto, index: number) => (
              <TouchableOpacity
                key={site.id}
                activeOpacity={0.8}
                onPress={() => handleSitePress(site, index)}
              >
                <LinearGradient
                  colors={selectedSite?.id === site.id 
                    ? [colors.primaryMain, '#1468B7'] // Primary blue to slightly lighter blue
                    : [colors.white, colors.neutral5]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={[
                    styles.siteCard,
                    selectedSite?.id === site.id && styles.selectedSiteCard
                  ]}
                >
                  <View style={styles.siteCardHeader}>
                    <MaterialCommunityIcons
                      name="lightning-bolt"
                      size={20}
                      color={selectedSite?.id === site.id ? colors.white : colors.primaryMain}
                    />
                    <Text 
                      style={[
                        styles.siteName,
                        selectedSite?.id === site.id && styles.selectedText
                      ]} 
                      numberOfLines={1}
                    >
                      {site.name}
                    </Text>
                  </View>
                  <Text 
                    style={[
                      styles.siteDescription,
                      selectedSite?.id === site.id && styles.selectedText
                    ]} 
                    numberOfLines={2}
                  >
                  {site.reference}
                  </Text>
                  {site && (
                    <View style={styles.addressContainer}>
                      <MaterialCommunityIcons
                        name="map-marker"
                        size={16}
                        color={selectedSite?.id === site.id ? colors.white : colors.neutral50}
                        style={styles.addressIcon}
                      />
                      <Text 
                        style={[
                          styles.siteAddress,
                          selectedSite?.id === site.id && styles.selectedText
                        ]} 
                        numberOfLines={2}
                      >
                        {site.name} {site.substations[0].perm_location.city}, {site.substations[0].perm_location.country}, {site.substations[0].perm_location.postcode}
                      </Text>
                    </View>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            ))
          )}
        </ScrollView>
      </View>

      {/* Action Button (Emergency Declaration or Request SAP) */}
      <View style={styles.actionButtonContainer}>
        {nearbySaps.length > 0 ? (
          <TouchableOpacity
            style={[
              styles.requestButton,
              !selectedSap && styles.requestButtonDisabled
            ]}
            onPress={handleRequestSap}
            activeOpacity={0.8}
            disabled={!selectedSap}
          >
            <MaterialCommunityIcons
              name="account-check"
              size={24}
              color={selectedSap ? colors.white : colors.neutral25}
              style={styles.actionButtonIcon}
            />
            <Text style={[
              styles.requestButtonText,
              !selectedSap && styles.requestButtonTextDisabled
            ]}>
              Request SAP
            </Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[
              styles.emergencyButton,
              !selectedSite && styles.emergencyButtonDisabled,
              isDeclaringEmergency && styles.emergencyButtonLoading
            ]}
            onPress={handleEmergencyPress}
            activeOpacity={0.8}
            disabled={!selectedSite || isDeclaringEmergency}
          >
            <MaterialCommunityIcons
              name="alert"
              size={24}
              color={selectedSite ? colors.white : colors.neutral25}
              style={styles.actionButtonIcon}
            />
            <Text style={[
              styles.emergencyButtonText,
              !selectedSite && styles.emergencyButtonTextDisabled
            ]}>
              Declare Emergency
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Emergency Declaration Sheet */}
      <EmergencyDeclarationSheet
        isVisible={showEmergencySheet}
        onClose={() => setShowEmergencySheet(false)}
        onSubmit={handleEmergencySubmit}
        isLoading={isDeclaringEmergency}
      />
    </View>
  );
};

export default SiteManagerMapScreen; 