import { StyleSheet, Platform, Dimensions } from 'react-native';
import colors from '../../styles/colors';

const { width } = Dimensions.get('window');
export const CARD_WIDTH = width * 0.8;
export const CARD_SPACING = 10;

export default StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  customMarker: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
    borderWidth: 2,
    borderColor: colors.primaryMain,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  selectedMarker: {
    backgroundColor: colors.primaryMain,
    borderColor: colors.white,
    transform: [{ scale: 1.1 }],
  },
  siteListContainer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    height: 130,
  },
  scrollViewContent: {
    paddingHorizontal: width * 0.1,
    paddingVertical: 5,
  },
  siteCard: {
    width: CARD_WIDTH,
    height: 120,
    borderRadius: 16,
    padding: 16,
    marginHorizontal: CARD_SPACING / 2,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  selectedSiteCard: {
    borderColor: colors.white,
    borderWidth: 1,
  },
  siteCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  siteName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginLeft: 8,
    flex: 1,
  },
  siteDescription: {
    fontSize: 14,
    color: colors.neutral50,
    marginBottom: 8,
    lineHeight: 20,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressIcon: {
    marginRight: 4,
  },
  siteAddress: {
    fontSize: 12,
    color: colors.neutral50,
    flex: 1,
  },
  selectedText: {
    color: colors.white,
  },
  emergencyButtonContainer: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.mainAlert,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 30,
    ...Platform.select({
      ios: {
        shadowColor: colors.mainAlert,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  emergencyButtonDisabled: {
    backgroundColor: colors.neutral5,
    ...Platform.select({
      ios: {
        shadowOpacity: 0.1,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  emergencyButtonLoading: {
    opacity: 0.8,
  },
  emergencyIcon: {
    marginRight: 8,
  },
  emergencyButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  emergencyButtonTextDisabled: {
    color: colors.neutral25,
  },
  errorContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    backgroundColor: colors.mainAlert,
    padding: 10,
    borderRadius: 8,
    opacity: 0.9,
  },
  errorText: {
    color: colors.white,
    textAlign: 'center',
    fontSize: 14,
  },
  sapMarker: {
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: 2,
    borderWidth: 2,
    borderColor: colors.primaryMain,
    width: 44,
    height: 44,
    alignItems: 'center',
    justifyContent: 'center',
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  selectedSapMarker: {
    backgroundColor: colors.primaryMain,
    borderColor: colors.white,
    transform: [{ scale: 1.1 }],
  },
  sapProfilePic: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.neutral5,
  },
  sapCard: {
    width: CARD_WIDTH,
    height: 120,
    borderRadius: 16,
    padding: 16,
    marginHorizontal: CARD_SPACING / 2,
    backgroundColor: colors.white,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  selectedSapCard: {
    borderColor: colors.white,
    borderWidth: 1,
  },
  sapCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sapCardProfilePic: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
    backgroundColor: colors.neutral5,
    borderWidth: 2,
    borderColor: colors.white,
  },
  selectedSapCardProfilePic: {
    borderColor: colors.white,
  },
  sapCardInfo: {
    flex: 1,
  },
  sapName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginBottom: 4,
  },
  sapRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sapRatingText: {
    fontSize: 14,
    color: colors.neutral75,
    marginLeft: 4,
  },
  sapCardDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sapDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sapDetailText: {
    fontSize: 14,
    color: colors.neutral50,
    marginLeft: 4,
  },
  actionButtonContainer: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
  },
  actionButtonIcon: {
    marginRight: 8,
  },
  requestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.mainSuccess,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 30,
    ...Platform.select({
      ios: {
        shadowColor: colors.mainSuccess,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
    }),
  },
  requestButtonDisabled: {
    backgroundColor: colors.neutral5,
    ...Platform.select({
      ios: {
        shadowOpacity: 0.1,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  requestButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
  requestButtonTextDisabled: {
    color: colors.neutral25,
  },
  emergencySiteMarker: {
    backgroundColor: colors.mainAlert,
    borderColor: colors.white,
    transform: [{ scale: 1.2 }],
    borderWidth: 3,
  },
  calloutMarker: {
    backgroundColor: colors.white,
    padding: 8,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: colors.primaryMain,
    elevation: 5,
    shadowColor: colors.neutral75,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  siteMarker: {
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
    borderWidth: 2,
    borderColor: colors.primaryMain,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  selectedSiteMarker: {
    backgroundColor: colors.primaryMain,
    borderColor: colors.white,
    transform: [{ scale: 1.1 }],
  },
  scrollView: {
    padding: 10,
  },
  cardsContainer: {
    padding: 10,
  },
  card: {
    width: CARD_WIDTH,
    height: 120,
    borderRadius: 16,
    marginHorizontal: CARD_SPACING / 2,
  },
  cardContent: {
    flex: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.neutral50,
  },
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  cardAddress: {
    fontSize: 12,
    color: colors.neutral50,
    marginLeft: 8,
  },
}); 