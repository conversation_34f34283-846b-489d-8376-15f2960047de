import React, { useState, useEffect, useRef } from 'react';
import {
    View,
    StyleSheet,
    Text,
    ScrollView,
    TouchableOpacity,
    Platform,
    Alert,
} from 'react-native';
import MapView, { Marker, Region } from 'react-native-maps';
import { useSelector } from 'react-redux';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { RootState } from '../../redux/store';
import colors from '../../styles/colors';
import {
    fetchSapActiveCallouts,
    ActiveCalloutLocation,
} from '../../services/workOrderService';
import styles, { CARD_WIDTH, CARD_SPACING } from './styles';

// Helper function to get fault type icon
const getFaultTypeIcon = (
    faultType: string,
): typeof MaterialCommunityIcons.prototype.props.name => {
    switch (faultType) {
        case 'COMPLETE_OUTAGE':
            return 'power-plug-off';
        case 'PARTIAL_OUTAGE':
            return 'power-plug-off-outline';
        case 'HAZARD':
            return 'alert-octagon';
        default:
            return 'help-circle';
    }
};

const SapMapScreen: React.FC = () => {
    const [activeCallouts, setActiveCallouts] = useState<
        ActiveCalloutLocation[]
    >([]);
    const mapRef = useRef<MapView>(null);
    const scrollViewRef = useRef<ScrollView>(null);
    const location = useSelector(
        (state: RootState) => state.location.currentLocation,
    );

    const getInitialRegion = (): Region => {
        if (location?.coords) {
            return {
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
            };
        }

        // Fallback to London
        return {
            latitude: 51.5074,
            longitude: -0.1278,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
        };
    };

    // Load SAP's active callouts
    useEffect(() => {
        const loadActiveCallouts = async () => {
            try {
                const callouts = await fetchSapActiveCallouts();
                setActiveCallouts(callouts);
            } catch (error) {
                console.error('Error loading active callouts:', error);
                Alert.alert('Error', 'Failed to load active callouts');
            }
        };

        loadActiveCallouts();
    }, []);

    // Animate to user location when it becomes available
    useEffect(() => {
        if (location?.coords && mapRef.current) {
            mapRef.current.animateToRegion(
                {
                    latitude: location.coords.latitude,
                    longitude: location.coords.longitude,
                    latitudeDelta: 0.0922,
                    longitudeDelta: 0.0421,
                },
                1000,
            );
        }
    }, [location]);

    return (
        <View style={styles.container}>
            <MapView
                ref={mapRef}
                style={styles.map}
                initialRegion={getInitialRegion()}
                showsUserLocation
                showsMyLocationButton
                provider={Platform.select({
                    ios: undefined, // Use Apple Maps on iOS
                    android: 'google', // Use Google Maps on Android
                })}>
                {/* Show active callouts */}
                {activeCallouts.map((callout) => (
                    <Marker
                        key={callout.id}
                        coordinate={{
                            latitude: callout.location.latitude,
                            longitude: callout.location.longitude,
                        }}
                        title={callout.site_name}
                        description={`${callout.fault_type} - ${callout.description}`}>
                        <View style={styles.calloutMarker}>
                            <MaterialCommunityIcons
                                name={getFaultTypeIcon(callout.fault_type)}
                                size={24}
                                color={
                                    callout.status === 'IN_PROGRESS'
                                        ? colors.mainYellow
                                        : colors.mainSuccess
                                }
                            />
                        </View>
                    </Marker>
                ))}
            </MapView>

            {/* Show active callout cards */}
            <View style={styles.cardsContainer} pointerEvents='box-none'>
                <ScrollView
                    ref={scrollViewRef}
                    horizontal
                    pagingEnabled
                    scrollEventThrottle={1}
                    showsHorizontalScrollIndicator={false}
                    snapToInterval={CARD_WIDTH + CARD_SPACING}
                    snapToAlignment='center'
                    contentContainerStyle={styles.scrollView}>
                    {activeCallouts.map((callout) => (
                        <TouchableOpacity
                            key={callout.id}
                            style={styles.card}
                            onPress={() => {
                                mapRef.current?.animateToRegion({
                                    latitude: callout.location.latitude,
                                    longitude: callout.location.longitude,
                                    latitudeDelta: 0.0922,
                                    longitudeDelta: 0.0421,
                                });
                            }}>
                            <View style={styles.cardContent}>
                                <View style={styles.cardHeader}>
                                    <MaterialCommunityIcons
                                        name={getFaultTypeIcon(
                                            callout.fault_type,
                                        )}
                                        size={24}
                                        color={
                                            callout.status === 'IN_PROGRESS'
                                                ? colors.mainYellow
                                                : colors.mainSuccess
                                        }
                                    />
                                    <Text style={styles.cardTitle}>
                                        {callout.site_name}
                                    </Text>
                                </View>
                                <Text style={styles.cardDescription}>
                                    {callout.description}
                                </Text>
                                <View style={styles.cardFooter}>
                                    <MaterialCommunityIcons
                                        name='map-marker'
                                        size={16}
                                        color={colors.neutral50}
                                    />
                                    <Text style={styles.cardAddress}>
                                        {callout.location.address_line_1},{' '}
                                        {callout.location.city}
                                    </Text>
                                </View>
                            </View>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};

export default SapMapScreen;
