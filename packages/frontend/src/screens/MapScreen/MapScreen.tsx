import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';
import SapMapScreen from './SapMapScreen';
import SiteManagerMapScreen from './SiteManagerMapScreen';

const MapScreen: React.FC = () => {
    const currentUser = useSelector(
        (state: RootState) => state.user.currentUser,
    );
    const isSap =
        currentUser &&
        'profile' in currentUser &&
        'user_type' in currentUser.profile &&
        currentUser.profile.user_type === 'SAP';

    return isSap ? <SapMapScreen /> : <SiteManagerMapScreen />;
};

export default MapScreen;
