import React, { useState, useEffect } from 'react';
import { View, SafeAreaView, StyleSheet, Text, Alert } from 'react-native';
import { Image } from 'expo-image';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import {
    useNavigation,
    NavigationProp,
    useRoute,
} from '@react-navigation/native';
import CheckBox from 'expo-checkbox';
import Logo from '../../assets/images/vectors/logo-light.svg';
import Watermark from '../../assets/images/raster/watermark.png';
import globalStyles from '../../styles/globalStyles';
import colors from '../../styles/colors';
import { combinedValue } from '../../utils/layout';
import PrimaryTextInput from '../../components/controls/PrimaryTextInput';
import TextButton from '../../components/controls/TextButton';
import PrimaryButton from '../../components/controls/PrimaryButton';
import { useLoader } from '../../contexts/LoaderContext';

import { AppDispatch } from '../../redux/store';
import { useDispatch, useSelector } from 'react-redux';
import { watchLocation } from '../../redux/slices/locationSlice';
import { RootState } from '../../redux/store';
import { authenticateUser } from '../../services/loginService';
import log from '../../utils/log';

type RootStackParamList = {
    ForgotPassword: undefined;
    Signup: undefined;
    Main: undefined;
};

const LoginScreen: React.FC = () => {
    const navigation = useNavigation<NavigationProp<RootStackParamList>>();
    const route = useRoute();
    const [email, setEmail] = useState<string>('');
    const [password, setPassword] = useState<string>('');
    const [rememberMe, setRememberMe] = useState<boolean>(false);
    const { loading, setLoading } = useLoader();

    // Redux
    const dispatch: AppDispatch = useDispatch<AppDispatch>();

    const currentUser = useSelector(
        (state: RootState) => state.user.currentUser,
    );
    const location = useSelector(
        (state: RootState) => state.location.currentLocation,
    );
    const locationError = useSelector(
        (state: RootState) => state.location.error,
    );

    const navigateToForgotPassword = () => {
        navigation.navigate('ForgotPassword');
    };

    const navigateToSignup = () => {
        navigation.navigate('Signup');
    };

    const navigateToMain = async () => {
        if (!email || !password) {
            Alert.alert(
                'Missing Information',
                'Please enter both email and password.',
                [{ text: 'OK' }],
            );
            return;
        }

        setLoading(true, {
            minimumLoadTime: 2 * 1000,
            dismissOnClick: false,
            onDismiss: () => {
                // Only navigate on successful login
            },
        });

        try {
            // Login
            log.info('Logging in...');
            await authenticateUser(email, password, dispatch);
            log.success('Successfully logged in!');

            // Start location services
            log.info('Dispatching location watcher...');
            await dispatch(watchLocation()).unwrap();
            log.success('Success!');

            // Complete loading and navigate only on success
            setLoading(false);
            navigation.navigate('Main');
        } catch (error: any) {
            log.debug('Error during login or location fetching: ', error);
            setLoading(false);

            Alert.alert(
                'Login Failed',
                error.message ||
                    'An unexpected error occurred. Please try again.',
                [{ text: 'OK' }],
            );
        }
    };

    // Handle user login
    useEffect(() => {
        if (currentUser) {
            navigation.navigate('Main');
        }
    }, [currentUser]);

    // Handle location error
    useEffect(() => {
        if (locationError) {
            log.debug('Location error: ', locationError);
        }
    }, [locationError]);

    return (
        <SafeAreaView style={globalStyles.safeArea}>
            <KeyboardAwareScrollView
                contentContainerStyle={globalStyles.container}
                keyboardShouldPersistTaps='never'
                scrollEnabled={false}>
                <Image
                    source={Watermark}
                    style={styles.watermark}
                    contentFit='contain'
                />

                <View style={styles.logoOuterContainer}>
                    <View style={styles.logoContainer}>
                        <Logo width='100%' height='100%' />
                    </View>
                </View>
                <Text style={styles.loginText}>LOGIN</Text>

                <View style={{ height: '3%' }} />

                <PrimaryTextInput
                    keyboardType='email-address'
                    style={{ marginTop: 10 }}
                    placeholder='email'
                    autoComplete='email'
                    value={email}
                    onChangeText={setEmail}
                />

                <PrimaryTextInput
                    keyboardType='default'
                    secureTextEntry={true}
                    showTogglePasswordIcon={true}
                    style={{ marginTop: 10 }}
                    placeholder='password'
                    autoComplete='current-password'
                    value={password}
                    onChangeText={setPassword}
                />

                <View style={styles.rememberMeContainer}>
                    <CheckBox
                        value={rememberMe}
                        onValueChange={setRememberMe}
                        color={rememberMe ? colors.primaryMain : undefined}
                        style={{ opacity: 0.7 }}
                    />
                    <Text style={styles.rememberMeText}>Remember Me</Text>
                    <View style={globalStyles.flexGrow} />

                    <TextButton
                        text='Forgot Password?'
                        textAlign='right'
                        onPress={navigateToForgotPassword}
                        style={{ marginEnd: 5 }}
                        color={colors.primaryLight}
                    />
                </View>

                <View style={{ height: '7%' }} />

                <PrimaryButton
                    onPress={navigateToMain}
                    text='Login'
                    style={{ marginBottom: 10 }}
                />

                <View style={styles.signupContainer}>
                    <Text style={styles.noAccountText}>
                        Don't have an account?
                    </Text>
                    <TextButton
                        text='Sign Up'
                        textAlign='left'
                        color={colors.lightBlue}
                        onPress={navigateToSignup}
                        style={{ marginStart: 5, paddingEnd: 15 }}
                    />
                </View>
            </KeyboardAwareScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    watermark: {
        position: 'absolute',
        width: '100%',
        height: '100%',
    },
    logoOuterContainer: {
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        height: '27.5%',
        marginTop: -10,
    },
    logoContainer: {
        width: '40%',
        height: 'auto',
        justifyContent: 'center',
        alignItems: 'center',
    },
    loginText: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 20,
    },
    rememberMeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 10,
        width: combinedValue(40, 140),
        paddingLeft: 2,
    },
    rememberMeText: {
        marginLeft: 5,
        fontSize: 14,
    },
    signupContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 5,
    },
    noAccountText: {
        fontSize: 15,
    },
});

export default LoginScreen;
