import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Platform,
  Alert,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import colors from '../../styles/colors';
import { fetchWorkOrders, WorkOrder, CalloutRequest, ActiveCallout } from '../../services/workOrderService';
import { acceptCalloutRequest, declineCalloutRequest } from '../../services/emergencyService';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';

const getFaultTypeIcon = (faultType: string) => {
  switch (faultType) {
    case 'COMPLETE_OUTAGE':
      return 'power-plug-off' as const;
    case 'PARTIAL_OUTAGE':
      return 'power-plug-off-outline' as const;
    case 'HAZARD':
      return 'alert-octagon' as const;
    default:
      return 'help-circle' as const;
  }
};

interface WorkOrderCardProps {
  item: WorkOrder;
  onAcceptRequest?: (id: string) => void;
  onDeclineRequest?: (id: string) => void;
}

const WorkOrderCard: React.FC<WorkOrderCardProps> = ({ item, onAcceptRequest, onDeclineRequest }) => {

  // safety check for item
  if (!item) return null;


  const isCalloutRequest = item.type === 'CALLOUT_REQUEST';
  const statusColor = isCalloutRequest ? colors.mainYellow : colors.mainSuccess;
  const statusText = isCalloutRequest 
    ? 'New Request'
    : item.status === 'IN_PROGRESS' ? 'In Progress' : 'Completed';

  return (
    <TouchableOpacity
      style={styles.card}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={[colors.white, colors.neutral5]}
        style={styles.cardContent}
      >
        {/* Header with Fault Type and Time */}
        <View style={styles.cardHeader}>
          <View style={styles.faultTypeContainer}>
            <MaterialCommunityIcons
              name={getFaultTypeIcon(item.fault_type)}
              size={24}
              color={colors.primaryMain}
            />
            <Text style={styles.faultTypeText}>
              {item.fault_type.replace('_', ' ')}
            </Text>
          </View>
          <Text style={styles.timeText}>
            {new Date(item.declaration_timestamp).toLocaleTimeString()}
          </Text>
        </View>

        {/* Site Info */}
        <Text style={styles.siteName}>{item.site_name}</Text>
        <Text style={styles.description} numberOfLines={2}>
          {item.description}
        </Text>

        {/* Location */}
        <View style={styles.locationContainer}>
          <MaterialCommunityIcons
            name="map-marker"
            size={16}
            color={colors.neutral50}
            style={styles.locationIcon}
          />
          <Text style={styles.locationText}>
            {/* {item.location.address_line_1}, {item.location.city} */}
          </Text>
        </View>

        {/* Additional Info for Callout Requests */}
        {isCalloutRequest && (
          <View style={styles.calloutInfo}>
            <View style={styles.infoItem}>
              <MaterialCommunityIcons name="clock-outline" size={16} color={colors.neutral50} />
              <Text style={styles.infoText}>
                {Math.round((item as CalloutRequest).estimated_time_mins / 60)}h {(item as CalloutRequest).estimated_time_mins % 60}m
              </Text>
            </View>
            <View style={styles.infoItem}>
              <MaterialCommunityIcons name="map-marker-distance" size={16} color={colors.neutral50} />
              <Text style={styles.infoText}>
                {(item as CalloutRequest).estimated_distance_miles} miles
              </Text>
            </View>
          </View>
        )}

        {/* Status Badge */}
        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
          <Text style={styles.statusText}>{statusText}</Text>
        </View>

        {/* Action Buttons for Callout Requests */}
        {isCalloutRequest && onAcceptRequest && onDeclineRequest && (
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.acceptButton]}
              onPress={() => onAcceptRequest(item.id)}
            >
              <MaterialCommunityIcons name="check-circle" size={20} color={colors.white} />
              <Text style={styles.actionButtonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.declineButton]}
              onPress={() => onDeclineRequest(item.id)}
            >
              <MaterialCommunityIcons name="close-circle" size={20} color={colors.white} />
              <Text style={styles.actionButtonText}>Decline</Text>
            </TouchableOpacity>
          </View>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const WorkOrdersScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const isSap = currentUser && 'profile' in currentUser && 'type' in currentUser.profile && currentUser.profile.type === 'Sap';

  const loadWorkOrders = async () => {
    try {
      const orders = await fetchWorkOrders(isSap);
      setWorkOrders(orders);
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to load work orders. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptRequest = async (calloutRequestId: string) => {
    try {
      await acceptCalloutRequest(calloutRequestId);
      Alert.alert(
        'Success',
        'Callout request accepted successfully!',
        [{ text: 'OK' }]
      );
      // Refresh the work orders list
      loadWorkOrders();
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to accept callout request. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleDeclineRequest = async (calloutRequestId: string) => {
    try {
      await declineCalloutRequest(calloutRequestId);
      Alert.alert(
        'Success',
        'Callout request declined.',
        [{ text: 'OK' }]
      );
      // Refresh the work orders list
      loadWorkOrders();
    } catch (error: any) {
      Alert.alert(
        'Error',
        error.message || 'Failed to decline callout request. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await loadWorkOrders();
    setRefreshing(false);
  }, []);

  useEffect(() => {
    loadWorkOrders();
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <MaterialCommunityIcons
          name="clipboard-text-clock-outline"
          size={48}
          color={colors.neutral25}
        />
        <Text style={styles.emptyText}>Loading work orders...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={workOrders}
        renderItem={({ item }) => (
          <WorkOrderCard 
            item={item} 
            onAcceptRequest={isSap && item.type === 'CALLOUT_REQUEST' ? handleAcceptRequest : undefined}
            onDeclineRequest={isSap && item.type === 'CALLOUT_REQUEST' ? handleDeclineRequest : undefined}
          />
        )}
        keyExtractor={item => item.id}
        contentContainerStyle={[
          styles.listContent,
          workOrders.length === 0 && styles.emptyListContent
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primaryMain}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialCommunityIcons
              name="clipboard-text-clock-outline"
              size={48}
              color={colors.neutral25}
            />
            <Text style={styles.emptyText}>No work orders available</Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContent: {
    padding: 16,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
  },
  card: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  faultTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  faultTypeText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: colors.primaryMain,
  },
  timeText: {
    fontSize: 12,
    color: colors.neutral50,
  },
  siteName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: colors.neutral50,
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    marginRight: 4,
  },
  locationText: {
    fontSize: 12,
    color: colors.neutral50,
  },
  calloutInfo: {
    flexDirection: 'row',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.neutral5,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  infoText: {
    fontSize: 12,
    color: colors.neutral50,
    marginLeft: 4,
  },
  statusBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    color: colors.white,
    fontWeight: '500',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  acceptButton: {
    backgroundColor: colors.mainSuccess,
  },
  declineButton: {
    backgroundColor: colors.mainAlert,
  },
  actionButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.neutral50,
  },
});

export default WorkOrdersScreen;
