import React, { useState } from 'react';
import { View, SafeAreaView, StyleSheet, Text, Alert, Platform } from 'react-native';
import { Image } from 'expo-image';
import {
  NavigationContainer,
  NavigationProp,
  useNavigation,
} from '@react-navigation/native';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Logo from '../../assets/images/vectors/logo-light.svg';
import Watermark from '../../assets/images/raster/watermark.png';
import globalStyles from '../../styles/globalStyles';
import colors from '../../styles/colors';
import TextButton from '../../components/controls/TextButton';
import PrimaryButton from '../../components/controls/PrimaryButton';
import PartnerSignupScreen from './PartnerSignupScreen';
import CustomerSignupScreen from './CustomerSignupScreen';


const Tab = createMaterialTopTabNavigator();

// Define the navigation type
type RootStackParamList = {
  Login: undefined;
  Main: undefined;
};

const SignupScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [partnerData, setPartnerData] = useState({
    email: '',
    phone: '',
    affiliation: '',
    password: '',
  });
  const [customerData, setCustomerData] = useState({
    email: '',
    phone: '',
    affiliation: '',
    password: '',
  });
  const [activeTab, setActiveTab] = useState<'Partner' | 'Customer'>('Partner');

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const submitData = () => {
    const data = activeTab === 'Partner' ? partnerData : customerData;
    const user = {
      email: data.email,
      phone: data.phone,
      affiliation: data.affiliation,
      type: activeTab,
    };

    // TODO: add to remote database
  };

  return (

    <SafeAreaView style={globalStyles.safeArea}>
    <KeyboardAwareScrollView
      contentContainerStyle={globalStyles.container}
      keyboardShouldPersistTaps='never'
      scrollEnabled={false}>
      <Image source={Watermark} style={styles.watermark} contentFit='contain' />

      <View style={styles.logoOuterContainer}>
        <View style={styles.logoContainer}>
          <Logo width='100%' height='100%' />
        </View>
      </View>
      <Text style={styles.signupText}>CREATE AN ACCOUNT</Text>
      <View style={styles.tabContainer}>
        <NavigationContainer independent={true}>
          <Tab.Navigator
            sceneContainerStyle={{ backgroundColor: 'transparent' }}
            screenOptions={{
              tabBarLabelStyle: { fontSize: 15, textTransform: 'none' },
              tabBarStyle: {
                backgroundColor: 'transparent',
                width: '80%',
                alignSelf: 'center',
              },
              tabBarActiveTintColor: colors.primaryMain,
              tabBarInactiveTintColor: 'rgba(0, 0, 0, 0.35)',
              tabBarIndicatorStyle: { backgroundColor: colors.primaryMain },
              tabBarIndicatorContainerStyle: {
                borderBottomWidth: 2,
                borderBottomColor: 'rgba(0, 0, 0, 0.1)',
                width: '100%',
              },
              tabBarPressColor: 'white'
            }}
            screenListeners={{
              state: (e) => {
                const tabName = e.data.state.routeNames[e.data.state.index] as
                  | 'Partner'
                  | 'Customer';
                setActiveTab(tabName);
              },
            }}>
            <Tab.Screen name='Partner'>
              {(props) => (
                <PartnerSignupScreen
                  {...props}
                  setData={(email, phone, affiliation, password) =>
                    setPartnerData({ email, phone, affiliation, password })
                  }
                />
              )}
            </Tab.Screen>
            <Tab.Screen name='Customer'>
              {(props) => (
                <CustomerSignupScreen
                  {...props}
                  setData={(email, phone, affiliation, password) =>
                    setCustomerData({ email, phone, affiliation, password })
                  }
                />
              )}
            </Tab.Screen>
          </Tab.Navigator>
        </NavigationContainer>
      </View>

      <PrimaryButton
        onPress={submitData}
        text='Sign Up'
        style={{ marginTop: 10 }}
      />
      <View style={styles.loginContainer}>
        <Text style={styles.alreadyAccountText}>Already have an account?</Text>
        <TextButton
          text='Login'
          textAlign='left'
          onPress={navigateToLogin}
          style={{ marginStart: 5 }}
        />
      </View>

      <Text style={styles.noticeText}>
        * the data above will be collected for marketing purposes (for MPN
        exclusively)
      </Text>
    </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  watermark: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  logoOuterContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: '27.5%',
    marginTop: -50,
  },
  logoContainer: {
    width: '40%',
    height: 'auto',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signupText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  loginContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  alreadyAccountText: {
    fontSize: 15,
  },
  tabContainer: {
    width: '100%',
    height: '40%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  noticeText: {
    position: 'absolute',
    bottom: 0,
    fontSize: 10,
    width: '75%',
    color: colors.neutral50,
    textAlign: 'center',
    marginBottom: Platform.OS === 'android' ? 15 : 0
  },
});

export default SignupScreen;
