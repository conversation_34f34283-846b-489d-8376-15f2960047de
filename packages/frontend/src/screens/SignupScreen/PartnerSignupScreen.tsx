import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import PrimaryTextInput from '../../components/controls/PrimaryTextInput';

const PartnerSignupScreen: React.FC<{
  setData: (
    email: string,
    phone: string,
    affiliation: string,
    password: string,
  ) => void;
}> = ({ setData }) => {
  const [email, setEmail] = useState<string>('');
  const [phone, setPhone] = useState<string>('');
  const [affiliation, setAffiliation] = useState<string>('');
  const [password, setPassword] = useState<string>('');

  React.useEffect(() => {
    setData(email, phone, affiliation, password);
  }, [email, phone, affiliation, password]);

  return (
    <ScrollView
      contentContainerStyle={{
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
      }}>
      <PrimaryTextInput
        keyboardType='email-address'
        style={{ marginTop: 10 }}
        placeholder='Enter your email'
        autoComplete='email'
        value={email}
        onChangeText={setEmail}
      />
      <PrimaryTextInput
        keyboardType='phone-pad'
        style={{ marginTop: 10 }}
        placeholder='Enter your phone number'
        autoComplete='tel'
        value={phone}
        onChangeText={setPhone}
      />
      <PrimaryTextInput
        keyboardType='default'
        style={{ marginTop: 10 }}
        placeholder='Enter your affiliation'
        autoComplete='off'
        value={affiliation}
        onChangeText={setAffiliation}
      />
      <PrimaryTextInput
        keyboardType='default'
        secureTextEntry={true}
        showTogglePasswordIcon={true}
        style={{ marginTop: 10 }}
        placeholder='Enter your password'
        autoComplete='one-time-code'
        value={password}
        onChangeText={setPassword}
      />
    </ScrollView>
  );
};

export default PartnerSignupScreen;
