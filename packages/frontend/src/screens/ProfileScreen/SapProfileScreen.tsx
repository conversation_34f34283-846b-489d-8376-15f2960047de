import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSelector } from 'react-redux';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import TopNavBackOnly from '../../components/menus/TopNavBackOnly';
import { RootState } from '../../redux/store';
import colors from '../../styles/colors';
import globalStyles from '../../styles/globalStyles';
import { Image } from 'expo-image';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';

type MaterialIconName = 'account' | 'account-circle' | 'email' | 'phone' | 'star' | 'map-marker-radius' | 'clock-outline' | 'domain' | 'domain-plus' | 'information-outline' | 'chart-bar';

interface SapStats {
  rating: number;
  total_callouts: number;
  avg_response_mins: number;
}

// Mock stats until backend provides them
const mockStats: SapStats = {
  rating: 4.8,
  total_callouts: 156,
  avg_response_mins: 45
};

const SapProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  if (!currentUser || !('profile' in currentUser) || !('type' in currentUser.profile) || currentUser.profile.type !== 'Sap') {
    return (
      <SafeAreaView style={globalStyles.safeArea}>
        <Text>Loading...</Text>
      </SafeAreaView>
    );
  }

  const { profile } = currentUser;
  // Use mock stats until backend provides them
  const stats = mockStats;

  return (
    <SafeAreaView style={globalStyles.safeArea}>
      <KeyboardAwareScrollView
        contentContainerStyle={globalStyles.container}
        keyboardShouldPersistTaps='never'
        scrollEnabled={false}>
        
        <View style={styles.container}>
          <TopNavBackOnly 
            onBackPress={() => navigation.goBack()} 
            isBackButtonRight={false}
            showEditButtonRight={false}
          />

          <ScrollView scrollEnabled={true}>
            {/* Profile Header */}
            <View style={styles.headerSection}>
              <View style={styles.headerContent}>
                <Image
                  source={require('../../assets/images/vectors/logo-light.svg')}
                  style={styles.logo}
                  resizeMode={'contain' satisfies 'cover' | 'contain' | 'stretch' | 'repeat' | 'center'}
                />
                {profile.profile_picture_url ? (
                  <Image
                    source={{ uri: profile.profile_picture_url }}
                    style={styles.profilePicture}
                    resizeMode={'contain' satisfies 'cover' | 'contain' | 'stretch' | 'repeat' | 'center'}
                  />
                ) : (
                  <View style={styles.profilePicturePlaceholder}>
                    <MaterialCommunityIcons name="account" size={40} color={colors.primaryMain} />
                  </View>
                )}
                <Text style={styles.name}>{profile.first_name} {profile.last_name}</Text>
                <View style={styles.ratingContainer}>
                  <MaterialCommunityIcons name="star" size={24} color={colors.mainYellow} />
                  <Text style={styles.rating}>{stats.rating.toFixed(1)}</Text>
                </View>
              </View>
            </View>

            {/* Personal Info */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="account" size={24} color={colors.primaryMain} />
                <Text style={styles.sectionTitle}>Personal Information</Text>
              </View>
              <View style={styles.infoContainer}>
                <InfoRow icon="account-circle" label="Name" value={`${profile.first_name} ${profile.last_name}`} />
                <InfoRow icon="email" label="Email" value={profile.email} />
                <InfoRow icon="phone" label="Phone" value={profile.phone_number} />
              </View>
            </View>

            {/* Partner Info */}
            {profile.partner && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <MaterialCommunityIcons name="domain" size={24} color={colors.primaryMain} />
                  <Text style={styles.sectionTitle}>Partner Information</Text>
                </View>
                <View style={styles.infoContainer}>
                  <InfoRow icon="domain-plus" label="Partner" value={profile.partner.name} />
                  <InfoRow icon="information-outline" label="Description" value={profile.partner.description} />
                </View>
              </View>
            )}

            {/* Statistics */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="chart-bar" size={24} color={colors.primaryMain} />
                <Text style={styles.sectionTitle}>Statistics</Text>
              </View>
              <View style={styles.statsContainer}>
                <StatItem icon="star" value={stats.rating.toFixed(1)} label="Rating" />
                <StatItem icon="map-marker-radius" value={stats.total_callouts.toString()} label="Total Callouts" />
                <StatItem icon="clock-outline" value={`${stats.avg_response_mins}m`} label="Avg. Response" />
              </View>
            </View>
          </ScrollView>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const InfoRow: React.FC<{ icon: MaterialIconName; label: string; value: string }> = ({ icon, label, value }) => (
  <View style={styles.infoRow}>
    <MaterialCommunityIcons name={icon} size={20} color={colors.neutral50} style={styles.infoIcon} />
    <View>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value}</Text>
    </View>
  </View>
);

const StatItem: React.FC<{ icon: MaterialIconName; value: string; label: string }> = ({ icon, value, label }) => (
  <View style={styles.statItem}>
    <MaterialCommunityIcons name={icon} size={24} color={colors.primaryMain} />
    <Text style={styles.statValue}>{value}</Text>
    <Text style={styles.statLabel}>{label}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 0,
    flex: 1,
  },
  headerSection: {
    backgroundColor: colors.primaryMain,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
  },
  headerContent: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginTop: -40,
  },
  logo: {
    width: 150,
    height: 40,
    marginBottom: 15,
    tintColor: colors.white,
  },
  profilePicture: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 12,
    backgroundColor: colors.white,
  },
  profilePicturePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  name: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 20,
    color: colors.white,
    marginLeft: 8,
  },
  section: {
    padding: 20,
    backgroundColor: colors.white,
    marginBottom: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginLeft: 10,
  },
  infoContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral5,
  },
  infoIcon: {
    marginRight: 15,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.neutral50,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: colors.neutral75,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 15,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: colors.neutral50,
    marginTop: 4,
  },
});

export default SapProfileScreen; 