import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSelector } from 'react-redux';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import TopNavBackOnly from '../../components/menus/TopNavBackOnly';
import { RootState } from '../../redux/store';
import colors from '../../styles/colors';
import globalStyles from '../../styles/globalStyles';
import { Image } from 'expo-image';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';
import { SiteDto } from '@po105-app/shared/interfaces/dto/models/site-dto';

type MaterialIconName = 'account' | 'account-circle' | 'email' | 'phone' | 'domain' | 'office-building' | 'map-marker-multiple' | 'lightning-bolt';

const SiteManagerProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  if (!currentUser || !('profile' in currentUser) || !('type' in currentUser.profile) || currentUser.profile.type !== 'SiteManager') {
    return (
      <SafeAreaView style={globalStyles.safeArea}>
        <Text>Loading...</Text>
      </SafeAreaView>
    );
  }

  const { profile } = currentUser;

  return (
    <SafeAreaView style={globalStyles.safeArea}>
      <KeyboardAwareScrollView
        contentContainerStyle={globalStyles.container}
        keyboardShouldPersistTaps='never'
        scrollEnabled={false}>
        
        <View style={styles.container}>
          <TopNavBackOnly 
            onBackPress={() => navigation.goBack()} 
            isBackButtonRight={false}
            showEditButtonRight={false}
          />

          <ScrollView scrollEnabled={true}>
            {/* Company Logo and Info */}
            <View style={styles.headerSection}>
              <View style={styles.headerContent}>
                <Image
                  source={require('../../assets/images/vectors/logo-light.svg')}
                  style={styles.logo}
                  resizeMode={'contain' satisfies 'cover' | 'contain' | 'stretch' | 'repeat' | 'center'}
                />
                {profile.company.profile_picture_url && (
                  <Image
                    source={{ uri: profile.company.profile_picture_url }}
                    style={styles.companyLogo}
                    resizeMode={'contain' satisfies 'cover' | 'contain' | 'stretch' | 'repeat' | 'center'}
                  />
                )}
                <Text style={styles.companyName}>{profile.company.name}</Text>
                <Text style={styles.companyDescription}>{profile.company.description}</Text>
              </View>
            </View>

            {/* User Info */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="account" size={24} color={colors.primaryMain} />
                <Text style={styles.sectionTitle}>Personal Information</Text>
              </View>
              <View style={styles.infoContainer}>
                <InfoRow icon="account-circle" label="Name" value={`${profile.first_name} ${profile.last_name}`} />
                <InfoRow icon="email" label="Email" value={profile.email} />
                <InfoRow icon="phone" label="Phone" value={profile.phone_number} />
              </View>
            </View>

            {/* Customer Info */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="domain" size={24} color={colors.primaryMain} />
                <Text style={styles.sectionTitle}>Customer Information</Text>
              </View>
              <View style={styles.infoContainer}>
                <InfoRow icon="office-building" label="Customer" value={profile.customer.name} />
              </View>
            </View>

            {/* Sites Summary */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialCommunityIcons name="map-marker-multiple" size={24} color={colors.primaryMain} />
                <Text style={styles.sectionTitle}>Managed Sites</Text>
              </View>
              <View style={styles.sitesContainer}>
                {profile.sites.map((site: SiteDto) => (
                  <View key={site.id} style={styles.siteCard}>
                    <MaterialCommunityIcons name="lightning-bolt" size={20} color={colors.primaryMain} />
                    <View style={styles.siteInfo}>
                      <Text style={styles.siteName}>{site.name}</Text>
                      <Text style={styles.siteDescription} numberOfLines={2}>{site.name}</Text>
                      {site && (
                        <Text style={styles.siteAddress}>
                          {/* {site.location.address_line_1}, {site.location.city} */}
                        </Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

const InfoRow: React.FC<{ icon: MaterialIconName; label: string; value: string }> = ({ icon, label, value }) => (
  <View style={styles.infoRow}>
    <MaterialCommunityIcons name={icon} size={20} color={colors.neutral50} style={styles.infoIcon} />
    <View>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value}</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 0,
    flex: 1,
  },
  headerSection: {
    backgroundColor: colors.primaryMain,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
  },
  headerContent: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    marginTop: -40,
  },
  logo: {
    width: 150,
    height: 40,
    marginBottom: 15,
    tintColor: colors.white,
  },
  companyLogo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 8,
    backgroundColor: colors.white,
  },
  companyName: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 3,
  },
  companyDescription: {
    fontSize: 16,
    color: colors.neutral5,
    textAlign: 'center',
  },
  section: {
    padding: 20,
    backgroundColor: colors.white,
    marginBottom: 10,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.neutral75,
    marginLeft: 10,
  },
  infoContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: colors.neutral5,
  },
  infoIcon: {
    marginRight: 15,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.neutral50,
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: colors.neutral75,
    fontWeight: '500',
  },
  sitesContainer: {
    gap: 10,
  },
  siteCard: {
    flexDirection: 'row',
    padding: 15,
    backgroundColor: colors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.neutral5,
    ...Platform.select({
      ios: {
        shadowColor: colors.neutral75,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  siteInfo: {
    flex: 1,
    marginLeft: 10,
  },
  siteName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.neutral75,
    marginBottom: 4,
  },
  siteDescription: {
    fontSize: 14,
    color: colors.neutral50,
    marginBottom: 4,
  },
  siteAddress: {
    fontSize: 12,
    color: colors.neutral50,
  },
});

export default SiteManagerProfileScreen; 