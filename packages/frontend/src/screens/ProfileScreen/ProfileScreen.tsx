import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { LoginResponse } from '@po105-app/shared/interfaces/dto/responses/login-response';
import SapProfileScreen from './SapProfileScreen';
import SiteManagerProfileScreen from './SiteManagerProfileScreen';

const ProfileScreen: React.FC = () => {
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const isSap = currentUser && 'profile' in currentUser && 'type' in currentUser.profile && currentUser.profile.type === 'Sap';

  return isSap ? <SapProfileScreen /> : <SiteManagerProfileScreen />;
};

export default ProfileScreen;
