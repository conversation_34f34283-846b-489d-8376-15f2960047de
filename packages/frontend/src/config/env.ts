import {
  API_URL as ENV_API_URL,
  API_TIMEOUT as ENV_API_TIMEOUT,
  APP_ENV as ENV_APP_ENV,
  APP_VERSION as ENV_APP_VERSION,
  DEBUG_MODE as ENV_DEBUG_MODE,
  ENABLE_ANALYTICS as ENV_ENABLE_ANALYTICS,
  ENABLE_CRASH_REPORTING as ENV_ENABLE_CRASH_REPORTING,
  SECURE_STORAGE_KEY_PREFIX as ENV_SECURE_STORAGE_KEY_PREFIX,
  LOCATION_UPDATE_INTERVAL as ENV_LOCATION_UPDATE_INTERVAL,
  LOCATION_DISTANCE_FILTER as ENV_LOCATION_DISTANCE_FILTER
} from '@env';


/* HELPER FUNCTIONS ======================================================== */

// string env variables (required)
const getRequiredEnvVariable = (key: string, value: string | undefined): string => {
    if (!value) {
        console.error(`${key} is not set in .env file`);
        throw new Error(`Environment variable ${key} is not set`);
    }
    return value;
};

// string env variables with default
const getEnvVariable = (key: string, value: string | undefined, defaultValue?: string): string => {
    if (!value && defaultValue === undefined) {
        console.error(`${key} is not set in .env file`);
        throw new Error(`Environment variable ${key} is not set`);
    }
    return value || defaultValue || '';
};

// number env variables (required)
const getRequiredNumberEnvVariable = (key: string, value: string | undefined): number => {
    if (!value) {
        console.error(`${key} is not set in .env file`);
        throw new Error(`Environment variable ${key} is not set`);
    }
    const numberValue = Number(value);
    if (isNaN(numberValue)) {
        console.error(`${key} is not a valid number`);
        throw new Error(`Environment variable ${key} is not a valid number`);
    }
    return numberValue;
};

// number env variables with default
const getNumberEnvVariable = (key: string, value: string | undefined, defaultValue?: number): number => {
    if (!value && defaultValue === undefined) {
        console.error(`${key} is not set in .env file`);
        throw new Error(`Environment variable ${key} is not set`);
    }
    if (!value) return defaultValue || 0;
    
    const numberValue = Number(value);
    if (isNaN(numberValue)) {
        console.error(`${key} is not a valid number`);
        throw new Error(`Environment variable ${key} is not a valid number`);
    }
    return numberValue;
};

// boolean env variables
const getBooleanEnvVariable = (key: string, value: string | undefined, defaultValue = false): boolean => {
    if (!value) {
        return defaultValue;
    }
    return value.toLowerCase() === 'true';
};

/* API CONFIGURATION ====================================================== */

export const API_URL = getRequiredEnvVariable('API_URL', ENV_API_URL);
export const API_TIMEOUT = getNumberEnvVariable('API_TIMEOUT', ENV_API_TIMEOUT, 30000);

/* APP CONFIGURATION ===================================================== */

export const APP_ENV = getEnvVariable('APP_ENV', ENV_APP_ENV, 'development');
export const APP_VERSION = getEnvVariable('APP_VERSION', ENV_APP_VERSION, '1.0.0');
export const DEBUG_MODE = getBooleanEnvVariable('DEBUG_MODE', ENV_DEBUG_MODE, true);

/* FEATURE FLAGS ======================================================== */

export const ENABLE_ANALYTICS = getBooleanEnvVariable('ENABLE_ANALYTICS', ENV_ENABLE_ANALYTICS, false);
export const ENABLE_CRASH_REPORTING = getBooleanEnvVariable('ENABLE_CRASH_REPORTING', ENV_ENABLE_CRASH_REPORTING, false);

/* STORAGE CONFIGURATION ================================================ */

export const SECURE_STORAGE_KEY_PREFIX = getEnvVariable('SECURE_STORAGE_KEY_PREFIX', ENV_SECURE_STORAGE_KEY_PREFIX, 'po105_');

/* GEOLOCATION CONFIGURATION ============================================ */

export const LOCATION_UPDATE_INTERVAL = getNumberEnvVariable('LOCATION_UPDATE_INTERVAL', ENV_LOCATION_UPDATE_INTERVAL, 5000);
export const LOCATION_DISTANCE_FILTER = getNumberEnvVariable('LOCATION_DISTANCE_FILTER', ENV_LOCATION_DISTANCE_FILTER, 10); 