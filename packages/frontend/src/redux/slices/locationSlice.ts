import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import * as Location from 'expo-location';
import { LocationState } from '../states';

const initialState: LocationState = {
  currentLocation: null,
  error: null,
};

export const watchLocation = createAsyncThunk<
  Location.LocationObject,                  // Success return type
  void,                                     // No arguments passed to the thunk
  { rejectValue: string }                   // Rejected value type
>(
  'location/watchLocation',
  async (_, { rejectWithValue }) => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        return rejectWithValue('Permission to access location was denied');
      }

      // Success      
      const location = await Location.getCurrentPositionAsync({});
      return location;
    } catch (error) {

      // Rejection
      return rejectWithValue('Error fetching location');
    }
  }
);

// SLICE
const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {},
  // REDUCERS
  extraReducers: (builder) => {
    builder
      // Success case handler
      .addCase(watchLocation.fulfilled, (state, action) => {
        state.currentLocation = action.payload;
        state.error = null;
      })
      // Rejection / error case handler
      .addCase(watchLocation.rejected, (state, action) => {
        state.error = action.payload as string;
      });
  },
});


// EXPORT ACTIONS / REDUCER
export default locationSlice.reducer;
