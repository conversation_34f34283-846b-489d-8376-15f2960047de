import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserState } from '../states';
// import { Customer, SAP } from '../../models';
import type { LoginResponse } from '@po105-app/shared/src/interfaces/dto/responses/login-response';

const initialState: UserState = { currentUser: null, error: null };

// SLICE
const userSlice = createSlice({
    name: 'user',
    initialState,
    // REDUCERS
    reducers: {
        setCurrentUser(state, action: PayloadAction<LoginResponse>) {
            state.currentUser = action.payload;
        },
        clearCurrentUser(state) {
            state.currentUser = null;
        },
    },
});

// EXPORT ACTIONS / REDUCER
export const { setCurrentUser, clearCurrentUser } = userSlice.actions;

export default userSlice.reducer;
