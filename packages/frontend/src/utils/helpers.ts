
/* IMPORTS */

import Constants from 'expo-constants';
import log from '@utils/log';

/* HELPER FUNCTIONS */
// Helper function to darken the color
export const darkenColor = (color: string, amount: number): string => {
  let colorValue = parseInt(color.slice(1), 16);
  let r = Math.max(0, ((colorValue >> 16) & 0xff) - Math.round(amount * 255));
  let g = Math.max(0, ((colorValue >> 8) & 0xff) - Math.round(amount * 255));
  let b = Math.max(0, (colorValue & 0xff) - Math.round(amount * 255));
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()}`;
};

// Helper function to lighten the color
export const lightenColor = (color: string, amount: number): string => {
  let colorValue = parseInt(color.slice(1), 16);
  let r = Math.min(255, ((colorValue >> 16) & 0xff) + Math.round(amount * 255));
  let g = Math.min(255, ((colorValue >> 8) & 0xff) + Math.round(amount * 255));
  let b = Math.min(255, (colorValue & 0xff) + Math.round(amount * 255));
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase()}`;
};


// Helper function to get device ID
export const getDeviceId = async (): Promise<string | undefined> => {
    try {
        return Constants.installationId;
    } catch (error) {
        log.warn('Could not get device ID:', error);
        return undefined;
    }
};

// Helper function to get IP address
export const getIpAddress = async (): Promise<string | undefined> => {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        log.warn('Could not get IP address:', error);
        return undefined;
    }
};
