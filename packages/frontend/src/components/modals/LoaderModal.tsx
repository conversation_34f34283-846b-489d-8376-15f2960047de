import React, { useState } from 'react';
import {
  View,
  Modal,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
} from 'react-native';
import BoltAnimation from '../animations/BoltAnimation';

interface LoaderModalProps {
  visible: boolean;
  onDismiss: () => void;
  dismissOnClick: boolean;
}

const LoaderModal: React.FC<LoaderModalProps> = ({
  visible,
  onDismiss,
  dismissOnClick,
}) => {
  const [colorIndex, setColorIndex] = useState(0);
  //   const [currentDot, setCurrentDot] = useState('');

  //   useEffect(() => {
  //     const timeout = setTimeout(() => {
  //         if (currentDot.length < 3) {
  //             setCurrentDot(currentDot + '.');
  //         } else {
  //             setCurrentDot('');
  //         }
  //     }, 250);
  //   }, [currentDot, setCurrentDot]);

  return (
    <Modal
      visible={visible}
      transparent
      animationType='fade'
      onRequestClose={onDismiss}>
      <TouchableWithoutFeedback
        onPress={dismissOnClick ? onDismiss : undefined}>
        <View style={styles.container}>
          <View style={styles.card}>
            <BoltAnimation />
            <View style={styles.textContainer}>
              <Text style={styles.text}>Powering up...</Text>
              {/* <Text style={styles.text}>{currentDot}</Text> */}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.35)',
  },
  card: {
    width: 200,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    alignItems: 'center',
  },
  textContainer: {
    marginTop: 20,
    flexDirection: 'row',
    // width: 100,
  },
  text: {
    fontSize: 16,
  },
  dots: {
    fontSize: 16,
  },
});

export default LoaderModal;
