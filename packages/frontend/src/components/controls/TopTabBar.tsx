import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import colors from '../../styles/colors';

const TopTabBar = (props: any) => {
  return (
    <View style={styles.tabBarContainer}>
      <View style={styles.tabBarContent}>
        {props.state.routes.map((route: any, index: number) => {
          const isFocused = props.state.index === index;
          const onPress = () => {
            const event = props.navigation.emit({
              type: 'tabPress',
              target: route.key,
            });

            if (!isFocused && !event.defaultPrevented) {
              props.navigation.navigate(route.name);
            }
          };

          return (
            <View key={route.key} style={styles.tabItem}>
              <Text
                onPress={onPress}
                style={[
                  styles.tabLabel,
                  {
                    color: isFocused
                      ? colors.primaryMain
                      : 'rgba(0, 0, 0, 0.35)',
                  },
                ]}>
                {route.name}
              </Text>
              {isFocused && <View style={styles.activeIndicator} />}
            </View>
          );
        })}
      </View>
      <View style={styles.fullWidthBottomBorder} />
    </View>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    width: '100%',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabBarContent: {
    width: '80%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
  },
  tabLabel: {
    fontSize: 15,
    textTransform: 'none',
  },
  activeIndicator: {
    width: '80%',
    height: 2,
    backgroundColor: colors.primaryMain,
    position: 'absolute',
  },
  fullWidthBottomBorder: {
    width: '100%',
    height: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    position: 'absolute',
    bottom: 0,
  },
});

export default TopTabBar;
