import React, { useState, ReactNode } from 'react';
import {
  Animated,
  Text,
  StyleSheet,
  GestureResponderEvent,
  ViewStyle,
  TextStyle,
  TouchableWithoutFeedback,
} from 'react-native';
import colors from '../../styles/colors';
import { combinedValue } from '../../utils/layout';

interface OutlineButtonProps {
  text: string | ReactNode;
  onPress: (event: GestureResponderEvent) => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  color?: string;
}

const OutlineButton: React.FC<OutlineButtonProps> = ({
  text,
  onPress,
  style,
  textStyle,
  color = colors.primaryMain,
}) => {
  const [animatedValue] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    Animated.spring(animatedValue, {
      toValue: 0.9,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(animatedValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const animatedStyle = {
    opacity: animatedValue,
    backgroundColor: animatedValue.interpolate({
      inputRange: [0.9, 1],
      outputRange: ['rgba(0,0,0,0.05)', 'transparent'], // Light shade when pressed
    }),
    borderColor: color,
  };

  return (
    <TouchableWithoutFeedback
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}>
      <Animated.View style={[styles.outlineButton, style, animatedStyle]}>
        {typeof text === 'string' ? (
          <Text style={[styles.outlineButtonText, { color }, textStyle]}>
            {text}
          </Text>
        ) : (
          text
        )}
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  outlineButton: {
    width: combinedValue(40, 140),
    height: 50,
    borderWidth: 1,
    borderRadius: 10,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: colors.primaryMain,
  },
  outlineButtonText: {
    color: colors.primaryMain,
    textTransform: 'none', // Ensure text is not uppercased
  },
});

export default OutlineButton;
