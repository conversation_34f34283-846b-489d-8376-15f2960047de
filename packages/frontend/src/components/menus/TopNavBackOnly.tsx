import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import BackArrowLeft from '../../assets/images/vectors/icons/back-arrow-left.svg';
import BackArrowRight from '../../assets/images/vectors/icons/back-arrow-right.svg';
import EditButton from '../../assets/images/vectors/icons/profile-edit-icon.svg';
import Logo from '../../assets/images/vectors/logo-text-only.svg';
import colors from '../../styles/colors';

interface TopNavBackOnlyProps {
    onBackPress: () => void;
    isBackButtonRight: boolean;
    showEditButtonRight: boolean;
}

const TopNavBackOnly: React.FC<TopNavBackOnlyProps> = ({ onBackPress, isBackButtonRight, showEditButtonRight }) => {
    return (
        <View style={styles.container}>
            {!isBackButtonRight && (
                <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
                    <BackArrowLeft width={25} height={25} />
                </TouchableOpacity>
            )}
            {isBackButtonRight && (
                <View style={styles.placeholderView} />
            )}
            <View style={styles.logoContainer}>
                <Logo width="85%" height="85%" />
            </View>
            {isBackButtonRight && (
                <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
                    <BackArrowRight width={25} height={25} />
                </TouchableOpacity>
            )}

            {!isBackButtonRight && !showEditButtonRight && (
                <View style={styles.placeholderView} />
            )}

            {!isBackButtonRight && showEditButtonRight && (
                <TouchableOpacity style={styles.backButton}>
                    <EditButton width={50} height={50} />
                </TouchableOpacity>
            )}

        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        width: '100%',
        height: 80,
        backgroundColor: colors.white,
        paddingBottom: 10,

        shadowColor: colors.neutral25, // IOS
        shadowOffset: { height: 1, width: 1 }, // IOS
        shadowOpacity: 0.6, // IOS
        shadowRadius: 3, //IOS
        elevation: 5,

        zIndex: 100,

        alignItems: 'center',
        justifyContent: 'space-between'
    },
    backButton: {
        marginLeft: 30,
        marginRight: 30,
        width: 50,
        justifyContent: 'center',
        alignItems: 'center'
    },
    logoContainer: {
        flex: 1,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center'
    },
    placeholderView: {
        marginLeft: 30,
        marginRight: 30,
        width: 50,
        justifyContent: 'center',
        alignItems: 'center'
    }
});

export default TopNavBackOnly;
