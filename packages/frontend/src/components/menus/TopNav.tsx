import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, StyleSheet, Image, ActivityIndicator } from 'react-native';
import HamburgerMenu from '../../assets/images/vectors/icons/hamburger-menu.svg';
import Logo from '../../assets/images/vectors/logo-text-only.svg';
import colors from '../../styles/colors';
import * as SecureStore from 'expo-secure-store';
import { SECURE_STORAGE_KEY_PREFIX, API_URL } from '@/config/env';

interface TopNavProps {
    onSidebarPress: () => void;
    onProfilePress: () => void;
    profileImage: string;
}

const svgMap: { [key: string]: React.FC<React.SVGProps<SVGSVGElement>> } = {
    'sap_1.svg': require('../../assets/images/vectors/fallback/sap-1.svg').default,
    'sap_2.svg': require('../../assets/images/vectors/fallback/sap-2.svg').default,
    'sap_3.svg': require('../../assets/images/vectors/fallback/sap-3.svg').default,
    'sap_4.svg': require('../../assets/images/vectors/fallback/sap-4.svg').default,
    'company_generic.svg': require('../../assets/images/vectors/fallback/company-generic.svg').default,
};

const TopNav: React.FC<TopNavProps> = ({ onSidebarPress, onProfilePress, profileImage }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [imageError, setImageError] = useState(false);
    const [cachedImageUrl, setCachedImageUrl] = useState<string | null>(null);
    const [authToken, setAuthToken] = useState<string | null>(null);
    
    useEffect(() => {
        const getAuthToken = async () => {
            try {
                const token = await SecureStore.getItemAsync(`${SECURE_STORAGE_KEY_PREFIX}token`);
                setAuthToken(token);
            } catch (error) {
                console.error('Error retrieving auth token:', error);
            }
        };
        getAuthToken();
    }, []);
    
    useEffect(() => {
        const isValidUrl = profileImage?.trim() && 
            (profileImage.startsWith('http://') || profileImage.startsWith('https://'));
        
        setCachedImageUrl(isValidUrl ? profileImage : null);
        setImageError(!isValidUrl);
    }, [profileImage]);
    
    const getImageSource = () => {
        if (!cachedImageUrl) return undefined;
        
        if (cachedImageUrl.includes('supabase.co') || cachedImageUrl.includes('supabase.in')) {
            return { uri: cachedImageUrl };
        }
        
        return authToken && cachedImageUrl.includes(API_URL)
            ? { uri: cachedImageUrl, headers: { Authorization: `Bearer ${authToken}` } }
            : { uri: cachedImageUrl };
    };
    
    const DefaultSvg = svgMap['sap_1.svg'];

    return (
        <View style={styles.container}>
            <TouchableOpacity onPress={onSidebarPress} style={styles.settingsButton}>
                <HamburgerMenu width={25} height={25} />
            </TouchableOpacity>
            <View style={styles.logoContainer}>
                <Logo width="85%" height="85%" />
            </View>
            <TouchableOpacity onPress={onProfilePress} style={styles.profileButtonContainer}>
                <View style={styles.profileButton}>
                    {!cachedImageUrl || imageError ? (
                        <DefaultSvg width={50} height={50} />
                    ) : (
                        <>
                            <Image 
                                source={getImageSource()}
                                style={styles.imageButton}
                                onLoadStart={() => setIsLoading(true)}
                                onLoadEnd={() => setIsLoading(false)}
                                onError={() => {
                                    setImageError(true);
                                    setIsLoading(false);
                                }}
                            />
                            {isLoading && (
                                <View style={styles.loaderContainer}>
                                    <ActivityIndicator size="small" color={colors.primaryMain} />
                                </View>
                            )}
                        </>
                    )}
                </View>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        width: '100%',
        height: 80,
        backgroundColor: colors.white,
        paddingBottom: 10,
        shadowColor: colors.neutral25,
        shadowOffset: { height: 1, width: 1 },
        shadowOpacity: 0.6,
        shadowRadius: 3,
        elevation: 5,
        zIndex: 100,
        alignItems: 'center',
        justifyContent: 'space-between'
    },
    settingsButton: {
        marginLeft: 30,
        width: 50,
        justifyContent: 'center',
        alignItems: 'center'
    },
    logoContainer: {
        flex: 1,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center'
    },
    profileButtonContainer: {
        marginRight: 30,
    },
    profileButton: {
        width: 50,
        height: 50,
        borderRadius: 27,
        borderWidth: 2,
        borderColor: colors.neutral25,
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        backgroundColor: colors.neutral0,
    },
    imageButton: {
        width: 50,
        height: 50,
        borderRadius: 25,
    },
    loaderContainer: {
        position: 'absolute',
        width: '100%',
        height: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
    },
});

export default TopNav;
