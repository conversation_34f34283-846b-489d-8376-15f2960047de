import React, {
  useEffect,
  useRef,
  Dispatch,
  SetStateAction,
  useState,
} from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import Svg, { Path, G } from 'react-native-svg';
import colors from '../../styles/colors';

const colorCycle = [
  colors.primaryMain,
  colors.primaryLight,
  colors.neutral50,
  colors.mainSuccess,
];

const AnimatedPath = Animated.createAnimatedComponent(Path);
const AnimatedG = Animated.createAnimatedComponent(G);

const BoltAnimation: React.FC = () => {
  const animatedVal = useRef(new Animated.Value(0)).current;
  const fillOpacity = useRef(new Animated.Value(0)).current;
  const fadeOpacity = useRef(new Animated.Value(0)).current;
  const [colorIndex, setColorIndex] = useState(0);

  useEffect(() => {
    const animate = () => {
      animatedVal.setValue(0);
      fillOpacity.setValue(0);
      fadeOpacity.setValue(1);

      // Sequence: draw the paths first, then fill the shape
      Animated.sequence([
        Animated.timing(animatedVal, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
          easing: Easing.inOut(Easing.sin),
        }),
        Animated.timing(fillOpacity, {
          toValue: 1,
          duration: 750,
          useNativeDriver: true,
          easing: Easing.linear,
        }),

        Animated.delay(150),

        Animated.timing(fadeOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
          easing: Easing.linear,
        }),

        Animated.delay(100),
      ]).start(() => {
        // update color index and repeat the animation
        requestAnimationFrame(() => {
            setColorIndex((prevIndex) => (prevIndex + 1) % colorCycle.length);
            animate();
        });
      });
    };

    animate();
  }, [animatedVal, fillOpacity, setColorIndex]);

  const strokeDashoffset = animatedVal.interpolate({
    inputRange: [0, 1],
    outputRange: [181, 0], // combined length for both paths
  });

  const fillOpacityInterpolate = fillOpacity.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  const fadeOpacityInterpolate = fadeOpacity.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <View style={styles.container}>
      <Animated.View style={{ opacity: fadeOpacityInterpolate }}>
        <Svg width={58} height={118} viewBox='0 0 57.9 117.5'>
          <AnimatedPath
            d='M54.5 0 L23.6 53.6 H58 L4.7 117.5'
            stroke={colorCycle[colorIndex]}
            strokeWidth='1'
            strokeDasharray='181'
            strokeDashoffset={strokeDashoffset}
            fill='none'
          />
          <AnimatedPath
            d='M54.5 0 L0 64 H35.7 L4.7 117.5'
            stroke={colorCycle[colorIndex]}
            strokeWidth='1'
            strokeDasharray='181'
            strokeDashoffset={strokeDashoffset}
            fill='none'
          />
          <AnimatedG opacity={fillOpacityInterpolate}>
            <Path
              d='M4.7,117.5L35.7,63.8H0L54.5,0L23.6,53.6H58L4.7,117.5Z'
              fill={colorCycle[colorIndex]}
            />
          </AnimatedG>
        </Svg>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default BoltAnimation;
