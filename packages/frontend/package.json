{"name": "po105-frontend", "version": "1.0.0", "description": "Mobile application for the PO105 Emergency Response Platform", "scripts": {"start": "npx expo start --go --tunnel", "dev": "expo start --dev-client", "lint": "echo \"No linting configured yet\" && exit 0", "clean": "rm -rf node_modules && rm -rf packages/*/node_modules"}, "dependencies": {"@babel/runtime": "^7.27.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^6.6.0", "@react-navigation/material-top-tabs": "^6.6.13", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.4.0", "@reduxjs/toolkit": "^2.2.6", "eas": "^0.1.0", "expo": "^53.0.9", "expo-checkbox": "~4.1.4", "expo-constants": "^17.0.8", "expo-dev-client": "~5.1.8", "expo-image": "~2.1.7", "expo-location": "~18.1.5", "expo-secure-store": "~14.2.3", "expo-sqlite": "~15.2.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-logs": "^5.1.0", "react-native-maps": "1.20.1", "react-native-pager-view": "6.7.1", "react-native-paper": "^4.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tab-view": "^3.5.2", "react-redux": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-runtime": "^7.26.10", "@expo/ngrok": "^4.1.3", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/react-native-dotenv": "^0.2.2", "babel-plugin-module-resolver": "^5.0.2", "prettier": "^3.3.2", "react-native-svg-transformer": "^1.5.0", "typescript": "~5.8.3"}, "author": "<PERSON>", "private": true}