{"compilerOptions": {"typeRoots": ["src/@types", "node_modules/@types"], "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "node16", "moduleResolution": "node16", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@screens/*": ["./src/screens/*"], "@hooks/*": ["./src/hooks/*"], "@utils/*": ["./src/utils/*"], "@styles/*": ["./src/styles/*"], "@navigation/*": ["./src/navigation/*"], "@services/*": ["./src/services/*"], "@redux/*": ["./src/redux/*"], "@contexts/*": ["./src/contexts/*"], "@po105-app/shared/*": ["../shared/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*"], "exclude": ["node_modules"], "extends": "expo/tsconfig.base"}