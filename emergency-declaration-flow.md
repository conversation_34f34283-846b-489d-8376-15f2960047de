# Emergency Flow Implementation Plan

## 1. Brief Overview

This document outlines the implementation plan for the emergency declaration and handling flow within the `po105-app`. It covers the user journey for both `SiteManagers` (Customers) declaring an emergency and `SAPs` (Engineers) responding to requests.

-   **Site Manager Flow:** Login -> View Map with Sites -> Click "Declare Emergency" -> Fill Emergency Details -> Select Nearby SAP(s) -> Await SAP Response.
-   **SAP Flow:** Login -> View List of Requests -> Accept/Reject Request -> If Accepted, View Map with Route to Site.

## 2. Core Concepts & Prerequisites

-   Authentication flow is complete and provides user context (type, ID).
-   Base `MapScreen.tsx` exists for Site Managers.
-   Backend architecture (Router->Controller->Service->Query) is established.
-   `db` (Kysely) and `supabase` clients are configured and accessible via `src/database/connection.ts`.
-   Shared interfaces reside in `packages/shared/src/interfaces`. New DTOs go into `dto/`, `dto/requests/`, or `dto/responses/`.
-   Database entities (`Callout`, `CalloutRequest`, `Site`, `Location`, `User`, `SapProfile`, etc.) are defined in `packages/shared/src/interfaces/entities`.

## 3. Implementation Phases & Tasks

---

### Phase 1: Site Manager - Declaring Emergency

*   **Goal:** Allow Site Managers to view their sites on a map and initiate an emergency declaration.

    *   **Task 1.1 (Frontend): Enhance `MapScreen` for Site Managers**
        *   **File:** `packages/frontend/src/screens/MapScreen/MapScreen.tsx`
        *   **Action:**
            *   Integrate a map component (e.g., `react-native-maps`). Center it initially based on user's sites or a default location.
            *   Fetch the logged-in Site Manager's associated sites and their locations (requires a backend endpoint or fetching logic during/after login).
            *   Display site locations as markers on the map.
            *   Implement a horizontally scrollable list of "Site Cards" (below the map or overlayed) showing basic site info. Tapping a card should animate the map to focus on that site's location.
            *   Add a prominent "Declare Emergency" button (e.g., floating action button or in the bottom navigation area if applicable).
        *   **UI/UX:** Map view dominates the screen. Site selector is easily accessible. Emergency button is clearly visible.

    *   **Task 1.2 (Shared): Define Callout DTOs**
        *   **Files:**
            *   `packages/shared/src/interfaces/dto/requests/callout.dto.ts`
            *   `packages/shared/src/interfaces/dto/responses/callout.dto.ts`
        *   **Action:**
            *   Define `CreateCalloutRequestDto`: Include `siteId`, `natureOfOutage` (string or enum based on `Callout` entity), `details` (text), `reportedById` (auto-filled from logged-in user). Refer to `Callout` entity for necessary fields.
            *   Define `CalloutResponseDto`: Include fields representing a created callout (e.g., `id`, `siteId`, `status`, `createdAt`, etc.).

    *   **Task 1.3 (Backend): Implement "Create Callout" Endpoint**
        *   **Files:**
            *   `packages/backend/src/routes/callout.routes.ts` (new)
            *   `packages/backend/src/controllers/callout.controller.ts` (new)
            *   `packages/backend/src/services/callout.service.ts` (new)
            *   Potentially `packages/backend/src/queries/callout.queries.ts` (new)
        *   **Action:**
            *   Create a POST route `/api/v1/callouts`. Add to Express app, likely protected for Site Managers.
            *   `CalloutController`: Define `createCallout` method. Validate request body against `CreateCalloutRequestDto`. Pass data to the service. Format response using `CalloutResponseDto`.
            *   `CalloutService`: Define `create` method. Take validated DTO and `userId`. Use `db` client (Kysely) to insert a new record into the `Callout` table with initial status (e.g., 'PENDING_SAP_SELECTION'). Return the newly created callout data. Use logger for significant events.

    *   **Task 1.4 (Frontend): Implement "Declare Emergency" Bottom Sheet**
        *   **Files:**
            *   `packages/frontend/src/screens/MapScreen/MapScreen.tsx`
            *   Potentially a new component `DeclareEmergencySheet.tsx`
        *   **Action:**
            *   When the "Declare Emergency" button is tapped, present a bottom sheet (e.g., using `@gorhom/bottom-sheet`).
            *   The sheet should contain input fields for the data required by `CreateCalloutRequestDto` (e.g., dropdown for nature of outage, text input for details). The `siteId` should be pre-filled based on the currently selected site on the map.
            *   Include a "Submit Emergency" button within the sheet.
            *   On submit, call the `POST /api/v1/callouts` endpoint (Task 1.3) with the collected data.
            *   Handle success (transition to SAP selection view - Phase 2) and error states (show messages).
        *   **UI/UX:** Clean, simple form within the bottom sheet. Clear indication of the selected site.

---

### Phase 2: Site Manager - Selecting SAPs

*   **Goal:** After declaring an emergency, allow the Site Manager to see and request nearby available SAPs.

    *   **Task 2.1 (Backend): Implement "Find Nearby SAPs" Endpoint**
        *   **Files:**
            *   `packages/backend/src/routes/sap.routes.ts` (new or existing)
            *   `packages/backend/src/controllers/sap.controller.ts` (new or existing)
            *   `packages/backend/src/services/sap.service.ts` (new or existing)
        *   **Action:**
            *   Create a GET route `/api/v1/saps/nearby` (or similar). It should accept query parameters for `latitude`, `longitude` (from the emergency site), and potentially `radius`.
            *   `SapController`: Define `findNearbySaps` method. Validate query params. Pass to service. Format response.
            *   `SapService`: Define `findNearby` method. Query `User` and `SapProfile` tables. Implement logic to filter SAPs based on:
                *   **Availability:** Requires an `isAvailable` flag or similar on `User` or `SapProfile` (needs DB migration if not present).
                *   **Distance:** Calculate distance between the site location (lat/lon) and SAP's location (requires SAPs to have a stored location - either fixed from `SapProfile` or dynamic if tracking is implemented later). Use geospatial functions if available, otherwise Haversine formula. Filter within the specified radius.
                *   Return a list of SAPs matching the criteria. Include `userId`, `firstName`, `lastName`, `profilePictureUrl`, average rating (requires rating system later), distance, partner company name (from `Partner` relation).
            *   **Database:** May require adding location columns (`latitude`, `longitude`) to `SapProfile` or a separate `SapLocation` table. May require adding an `isAvailable` boolean to `User` or `SapProfile`.

    *   **Task 2.2 (Shared): Define Nearby SAP DTO**
        *   **File:** `packages/shared/src/interfaces/dto/responses/sap.dto.ts` (new or existing)
        *   **Action:**
            *   Define `NearbySapDto`: Include fields needed for the frontend list (e.g., `userId`, `firstName`, `lastName`, `profilePictureUrl`, `distance`, `rating`, `partnerName`).

    *   **Task 2.3 (Frontend): Display Nearby SAPs View**
        *   **File:** `packages/frontend/src/screens/MapScreen/MapScreen.tsx` or a new sub-component.
        *   **Action:**
            *   After successfully creating a callout (Task 1.4), transition the UI. Keep the map visible, centered on the emergency site.
            *   Call the `GET /api/v1/saps/nearby` endpoint (Task 2.1) with the site's coordinates.
            *   Display a bottom sheet or a view covering the bottom half of the screen.
            *   Inside this view, render a scrollable list of available SAPs using the data (`NearbySapDto[]`) from the backend. Show key info (name, picture, rating, distance).
            *   Allow the Site Manager to select one or more SAPs from the list (e.g., checkboxes or highlighting).
            *   Add a "Request Selected SAP(s)" button, enabled only when at least one SAP is selected.
        *   **UI/UX:** Half map, half scrollable list of SAPs. Clear selection indicators.

    *   **Task 2.4 (Shared): Define Callout Request DTOs**
        *   **Files:**
            *   `packages/shared/src/interfaces/dto/requests/calloutRequest.dto.ts` (new)
            *   `packages/shared/src/interfaces/dto/responses/calloutRequest.dto.ts` (new)
        *   **Action:**
            *   Define `CreateCalloutRequestRequestDto`: Include `calloutId` and an array `sapUserIds`.
            *   Define `CalloutRequestResponseDto`: Represent the outcome, perhaps just a success boolean or an array of the created request IDs and their initial status.

    *   **Task 2.5 (Backend): Implement "Create Callout Requests" Endpoint**
        *   **Files:**
            *   `packages/backend/src/routes/calloutRequest.routes.ts` (new)
            *   `packages/backend/src/controllers/calloutRequest.controller.ts` (new)
            *   `packages/backend/src/services/calloutRequest.service.ts` (new)
        *   **Action:**
            *   Create a POST route `/api/v1/callout-requests`. Protect for Site Managers.
            *   `CalloutRequestController`: Define `createRequests` method. Validate body against `CreateCalloutRequestRequestDto`. Pass to service.
            *   `CalloutRequestService`: Define `createMultiple` method. Take `calloutId` and `sapUserIds` array.
                *   Verify the `calloutId` belongs to the authenticated user.
                *   Loop through `sapUserIds`. For each SAP, create a new record in the `CalloutRequest` table linking `calloutId` and `sapUserId`. Set initial status to `PENDING`.
                *   Consider transaction safety if multiple inserts must succeed or fail together.
                *   **(Future):** Trigger notifications to the requested SAPs (e.g., push notifications, Supabase Realtime event).
                *   Return success status or details of created requests (`CalloutRequestResponseDto`).

    *   **Task 2.6 (Frontend): Send Requests and Show Pending Status**
        *   **File:** `packages/frontend/src/screens/MapScreen/MapScreen.tsx` or sub-component.
        *   **Action:**
            *   When "Request Selected SAP(s)" (Task 2.3) is pressed, call the `POST /api/v1/callout-requests` endpoint (Task 2.5) with the `calloutId` and selected `sapUserIds`.
            *   On success, update the UI to a "Pending" state. The bottom sheet/view might now show the list of *requested* SAPs and their status (initially "Pending" for all).
            *   Disable further SAP selection/requesting for this callout for now (or update logic if adding more SAPs later is desired).
        *   **UI/UX:** Clear indication that requests have been sent and are awaiting responses. Show status per requested SAP.

---

### Phase 3: SAP - Handling Requests

*   **Goal:** Allow SAPs to view, accept, or reject incoming callout requests.

    *   **Task 3.1 (Shared): Define SAP's Callout Request DTO**
        *   **File:** `packages/shared/src/interfaces/dto/responses/calloutRequest.dto.ts` (or a new `sap.dto.ts`)
        *   **Action:**
            *   Define `SapCalloutRequestDto`: Include all details needed for the SAP's list view, such as `requestId`, `calloutId`, `requestStatus`, `requestedAt`, `siteName`, `siteAddress`, `siteLocation` (lat/lon), `natureOfOutage`, `customerName` (Site Manager), `customerCompany`. Join data from `CalloutRequest`, `Callout`, `Site`, `Location`, `User` (SiteManager).

    *   **Task 3.2 (Backend): Implement Endpoint for SAPs to Fetch Their Requests**
        *   **Files:**
            *   `packages/backend/src/routes/calloutRequest.routes.ts`
            *   `packages/backend/src/controllers/calloutRequest.controller.ts`
            *   `packages/backend/src/services/calloutRequest.service.ts`
        *   **Action:**
            *   Create a GET route `/api/v1/callout-requests/my-requests`. Protect for SAPs.
            *   `CalloutRequestController`: Define `getMyRequests` method. Call service. Format response using `SapCalloutRequestDto`.
            *   `CalloutRequestService`: Define `findBySapId` method. Get authenticated SAP's `userId`. Query `CalloutRequest` table, performing necessary joins (`Callout`, `Site`, `Location`, `User` for Site Manager info) to gather all data for `SapCalloutRequestDto`. Filter by `sapUserId`. Order results (e.g., `PENDING` status first, then by `createdAt` descending). Return the list.

    *   **Task 3.3 (Frontend): Implement SAP Request List Screen**
        *   **Files:**
            *   `packages/frontend/src/screens/SapRequestListScreen/SapRequestListScreen.tsx` (new)
            *   `packages/frontend/src/navigation/` (update navigation logic for SAPs)
        *   **Action:**
            *   Create the `SapRequestListScreen`. This should be the primary screen shown to SAPs after login.
            *   Fetch data using the `GET /api/v1/callout-requests/my-requests` endpoint (Task 3.2).
            *   Display the requests (`SapCalloutRequestDto[]`) in a list (e.g., `FlatList`).
            *   Group or visually separate requests: Active (`PENDING` status) should be prominent at the top, followed by other statuses (ACCEPTED, REJECTED, COMPLETED) potentially in sections or visually distinct.
            *   Each list item should show key summary info (Site Name, Nature of Outage, Time Requested, Status).
            *   Allow tapping on a list item to navigate to a details screen (or expand the item) to see full details and action buttons (Accept/Reject).
        *   **UI/UX:** Clear, actionable list. Prioritize pending requests.

    *   **Task 3.4 (Shared): Define Status Update DTO**
        *   **File:** `packages/shared/src/interfaces/dto/requests/calloutRequest.dto.ts`
        *   **Action:**
            *   Define `UpdateCalloutRequestStatusRequestDto`: Include `status` (string enum: 'ACCEPTED', 'REJECTED').

    *   **Task 3.5 (Backend): Implement Endpoint for SAPs to Accept/Reject**
        *   **Files:**
            *   `packages/backend/src/routes/calloutRequest.routes.ts`
            *   `packages/backend/src/controllers/calloutRequest.controller.ts`
            *   `packages/backend/src/services/calloutRequest.service.ts`
        *   **Action:**
            *   Create a PATCH route `/api/v1/callout-requests/:requestId/status`. Protect for SAPs.
            *   `CalloutRequestController`: Define `updateRequestStatus` method. Get `requestId` from params, `status` from body (`UpdateCalloutRequestStatusRequestDto`). Validate. Pass to service.
            *   `CalloutRequestService`: Define `updateStatus` method. Take `requestId`, new `status`, and authenticated SAP's `userId`.
                *   Verify the `CalloutRequest` exists and belongs to this SAP (`sapUserId` matches).
                *   Verify the current status allows this transition (e.g., can only accept/reject if `PENDING`).
                *   Update the `status` field in the `CalloutRequest` table for the given `requestId`.
                *   **(Future):** Trigger notifications/updates to the Site Manager (e.g., via WebSockets/Supabase Realtime).
                *   **(Optional):** If accepted, potentially update the parent `Callout` record's status (e.g., to 'SAP_ASSIGNED' or 'EN_ROUTE'). Consider what happens if multiple SAPs were requested and one accepts - maybe other pending requests for the *same* callout should be cancelled/updated? Define this business logic.
                *   Return the updated `CalloutRequest` details.

    *   **Task 3.6 (Frontend): Implement SAP Accept/Reject Actions**
        *   **Files:**
            *   `packages/frontend/src/screens/SapRequestListScreen/SapRequestListScreen.tsx` (or detail view)
        *   **Action:**
            *   In the request detail view (or on the list item for quick actions), provide "Accept" and "Reject" buttons, visible only for requests with `PENDING` status.
            *   On button press, call the `PATCH /api/v1/callout-requests/:requestId/status` endpoint (Task 3.5) with the appropriate status.
            *   On success:
                *   If **Rejected**, update the request status in the list view.
                *   If **Accepted**, navigate the SAP to the Map View (Phase 4). Refresh the request list data.
            *   Handle errors (e.g., show a message).

---

### Phase 4: Post-Acceptance & Real-time Updates

*   **Goal:** Show routing for accepted SAPs and update Site Manager view. (Real-time aspects can be deferred).

    *   **Task 4.1 (Frontend - SAP): Implement Map View for Accepted Requests**
        *   **Files:**
            *   `packages/frontend/src/screens/SapJourneyScreen/SapJourneyScreen.tsx` (new)
            *   `packages/frontend/src/navigation/`
        *   **Action:**
            *   Create `SapJourneyScreen`. Navigate here after an SAP accepts a request (Task 3.6).
            *   Receive necessary data (site location, callout details) via navigation params.
            *   Display a map centered between the SAP's current location and the site location.
            *   Requires obtaining SAP's current location (use `expo-location` or similar, request permissions).
            *   Display markers for SAP's current location and the destination site.
            *   Integrate with a routing service (e.g., Google Maps Directions API via `react-native-maps-directions`) to fetch and draw the route on the map.
            *   **(Future):** Implement background location tracking to update the SAP's position marker while navigating.
        *   **UI/UX:** Clear map showing SAP location, site location, and the route. Potentially show ETA.

    *   **Task 4.2 (Backend/Infra): Implement Real-time Updates (Optional - Deferrable)**
        *   **Platform:** Supabase Realtime or WebSockets (e.g., Socket.IO with Node.js backend).
        *   **Action:**
            *   Set up the chosen real-time mechanism.
            *   **Backend:** Modify services (`CalloutRequestService`, potentially `LocationService` if tracking SAPs) to publish events when:
                *   A `CalloutRequest` status changes (Accepted, Rejected).
                *   An SAP's location updates (if tracking).
            *   **Frontend:** Implement subscription logic:
                *   `SiteManager` (`MapScreen`): Subscribe to updates for their active `CalloutRequests`. Update the status of requested SAPs in the UI when events are received. If an SAP accepts, potentially show their location/ETA if that data is broadcast.
                *   `SAP` (`SapJourneyScreen`): Could potentially receive messages or updates related to the job.

    *   **Task 4.3 (Frontend - Site Manager): Update UI on SAP Status Change**
        *   **File:** `packages/frontend/src/screens/MapScreen/MapScreen.tsx`
        *   **Action:**
            *   If using Real-time (Task 4.2): Implement the subscription logic described above.
            *   If **not** using Real-time: Implement periodic polling or a manual refresh mechanism on the screen showing pending requests. Refetch the status of the `Callout` and its associated `CalloutRequests`.
            *   Update the UI list/indicators for requested SAPs to reflect their current status (Accepted, Rejected, etc.).
            *   If an SAP status changes to 'ACCEPTED', clearly indicate this. Potentially change the view state to show the assigned SAP is on the way. (Future: Show SAP on map).

---

This breakdown provides a structured approach. Each task is designed to be manageable and builds upon the previous ones. Remember to write tests, handle errors gracefully, and adhere to the established coding standards throughout the implementation.