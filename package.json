{"name": "po105-app", "version": "0.1.0", "description": "PO105 Mobile Application and Backend Server", "private": true, "scripts": {"build": "turbo run build", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "generate-types": "turbo run generate-types", "dev": "turbo run dev", "start": "turbo run start"}, "engines": {"node": ">=16.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/Midlands-Power-Networks/po105-app.git"}, "keywords": ["emergency", "response", "platform", "reactnative", "express"], "bugs": {"url": "https://github.com/Midlands-Power-Networks/po105-app/issues"}, "homepage": "https://github.com/Midlands-Power-Networks/po105-app#readme", "devDependencies": {"@types/cors": "^2.8.18", "@types/geojson": "^7946.0.16", "@types/node": "^22.15.18", "@types/swagger-jsdoc": "^6.0.4", "concurrently": "^8.2.2", "geojson": "^0.5.0", "openapi-types": "^12.1.3", "react-native-dotenv": "^3.4.11", "turbo": "^2.5.3", "typescript": "^5.8.3"}, "pnpm": {"overrides": {"bcrypt": "^5.1.1"}, "onlyBuiltDependencies": ["@biomejs/biome", "@nestjs/core", "bcrypt", "esbuild", "lefthook"]}, "packageManager": "pnpm@10.10.0", "dependencies": {"@expo/vector-icons": "^14.0.4", "@gorhom/bottom-sheet": "^5.1.2", "@react-native-picker/picker": "^2.11.0", "expo-haptics": "^14.0.1", "expo-linear-gradient": "^14.0.2"}}